import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * 获取应用列表
 * @param page 页码
 * @param pageSize 每页大小
 */
export function fetchAppList(page: number = 1, pageSize: number = 20) {
  return request<Api.App.AppListResponse>({
    url: '/api/v1/applications',
    method: 'get',
    params: {
      page,
      page_size: pageSize
    }
  });
}

/**
 * 创建应用
 * @param data 创建应用请求数据
 */
export function fetchCreateApp(data: Api.App.CreateAppRequest) {
  const requestData: any = {
    app_name: data.app_name,
    logo_url: data.logo_url, // 必填字段
    ip_whitelist: data.ip_whitelist // 必填字段
  };

  return request<Api.App.AppListItem>({
    url: '/api/v1/applications',
    method: 'post',
    data: requestData
  });
}

/**
 * 更新应用
 * @param data 更新应用请求数据
 */
export function fetchUpdateApp(data: Api.App.UpdateAppRequest) {
  const requestData: any = {
    nickname: data.nickname
  };

  if (data.icon) {
    requestData.icon = data.icon;
  }

  if (data.whitelist) {
    requestData.whitelist = data.whitelist;
  }

  return request<Api.App.AppListItem>({
    url: `/api/v1/applications/${data.id}`,
    method: 'put',
    data: requestData
  });
}

/**
 * 删除应用
 * @param id 应用ID
 */
export function fetchDeleteApp(id: number) {
  return request<{ success: boolean }>({
    url: `/api/v1/applications/${id}`,
    method: 'delete'
  });
}
