<template>
  <div class="h-full overflow-hidden">
    <NSpace vertical :size="16" class="h-full">
      <!-- 账户余额显示 -->
      <NCard title="账户余额" :bordered="false" class="card-wrapper">
        <div class="flex items-center gap-4">
          <div class="text-2xl font-bold text-primary">
            ¥{{ (typeof balance === 'number' ? balance : 0).toFixed(2) }}
          </div>
          <NButton
            type="primary"
            ghost
            size="small"
            @click="getAccountBalance"
            :loading="balanceLoading"
          >
            刷新余额
          </NButton>
        </div>
      </NCard>

      <!-- 资源包列表和购买记录 -->
      <div class="flex-1 flex gap-16px overflow-hidden">
        <!-- 资源包列表 -->
        <NCard title="资源包列表" :bordered="false" class="flex-1 card-wrapper">
          <template #header-extra>
            <NButton @click="getPackageList" :loading="packagesLoading">
              刷新
            </NButton>
          </template>
          <div class="h-full overflow-auto">
            <div v-if="packagesLoading" class="flex justify-center items-center h-32">
              <NSpin size="medium" />
            </div>
            <div v-else-if="packages.length === 0" class="flex justify-center items-center h-32 text-gray-500">
              暂无资源包
            </div>
            <div v-else class="grid gap-4 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
              <NCard
                v-for="pkg in packages"
                :key="pkg.id"
                :bordered="true"
                class="package-card cursor-pointer hover:shadow-lg transition-shadow"
              >
                <div class="space-y-3">
                  <div class="flex justify-between items-start">
                    <h3 class="text-lg font-semibold text-gray-800">{{ pkg.package_name }}</h3>
                    <NTag type="info" size="small">{{ pkg.sort_order }}级</NTag>
                  </div>
                  
                  <p class="text-gray-600 text-sm">{{ pkg.package_description }}</p>
                  
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-gray-500">调用次数:</span>
                      <span class="font-medium">{{ pkg.quota_amount.toLocaleString() }}次</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">有效期:</span>
                      <span class="font-medium">{{ pkg.validity_days }}天</span>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-gray-500">价格:</span>
                      <span class="text-xl font-bold text-primary">¥{{ pkg.price }}</span>
                    </div>
                  </div>
                  
                  <NButton
                    type="primary"
                    block
                    :loading="purchasingPackageId === pkg.id"
                    :disabled="balance < pkg.price"
                    @click="handlePurchasePackage(pkg)"
                  >
                    {{ balance < pkg.price ? '余额不足' : '立即购买' }}
                  </NButton>
                </div>
              </NCard>
            </div>
          </div>
        </NCard>

        <!-- 购买记录 -->
        <NCard title="我的资源包" :bordered="false" class="flex-1 card-wrapper">
          <template #header-extra>
            <div class="flex gap-2">
              <NSelect
                v-model:value="recordsFilter.package_id"
                placeholder="筛选资源包"
                :options="packageFilterOptions"
                clearable
                class="w-40"
                @update:value="handleFilterChange"
              />
              <NButton @click="getPurchaseRecords(1)" :loading="recordsLoading">
                刷新
              </NButton>
            </div>
          </template>
          <div class="h-full">
            <div v-if="!recordsLoading && purchaseRecords.length === 0" class="flex flex-col justify-center items-center h-64 text-gray-500">
              <div class="text-4xl mb-4">📦</div>
              <div class="text-lg mb-2">暂无资源包</div>
              <div class="text-sm">购买资源包后将显示在这里</div>
            </div>
            <NDataTable
              v-else
              :columns="recordColumns"
              :data="purchaseRecords"
              :loading="recordsLoading"
              :pagination="{
                page: recordsPagination.page,
                pageSize: recordsPagination.pageSize,
                itemCount: recordsPagination.itemCount,
                showSizePicker: true,
                pageSizes: [10, 20, 50],
                onUpdatePage: handleRecordsPageChange,
                onUpdatePageSize: (pageSize: number) => {
                  recordsPagination.pageSize = pageSize;
                  getPurchaseRecords(1);
                }
              }"
              :scroll-x="800"
              class="h-full"
            />
          </div>
        </NCard>
      </div>
    </NSpace>

    <!-- 购买确认弹窗 -->
    <NModal v-model:show="showPurchaseModal" preset="dialog" title="确认购买">
      <div v-if="selectedPackage" class="space-y-4">
        <div class="text-center">
          <h3 class="text-lg font-semibold mb-2">{{ selectedPackage.package_name }}</h3>
          <p class="text-gray-600 mb-4">{{ selectedPackage.package_description }}</p>
        </div>
        
        <div class="bg-gray-50 p-4 rounded-lg space-y-2">
          <div class="flex justify-between">
            <span>调用次数:</span>
            <span class="font-medium">{{ selectedPackage.quota_amount.toLocaleString() }}次</span>
          </div>
          <div class="flex justify-between">
            <span>有效期:</span>
            <span class="font-medium">{{ selectedPackage.validity_days }}天</span>
          </div>
          <div class="flex justify-between">
            <span>价格:</span>
            <span class="text-xl font-bold text-primary">¥{{ selectedPackage.price }}</span>
          </div>
          <div class="flex justify-between border-t pt-2">
            <span>当前余额:</span>
            <span class="font-medium">¥{{ balance.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between">
            <span>购买后余额:</span>
            <span class="font-medium">¥{{ (balance - selectedPackage.price).toFixed(2) }}</span>
          </div>
        </div>
      </div>
      
      <template #action>
        <NSpace>
          <NButton @click="showPurchaseModal = false">取消</NButton>
          <NButton type="primary" :loading="purchaseLoading" @click="confirmPurchase">
            确认购买
          </NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, h } from 'vue';
import { NCard, NSpace, NButton, NDataTable, NTag, NModal, NSelect, NSpin, useMessage } from 'naive-ui';
import { fetchAccountBalance, fetchPackageList, fetchPurchasePackage, fetchPackagePurchaseRecords } from '@/service/api';
import type { Api } from '@/typings/api';

defineOptions({
  name: 'ResourcePackages'
});

const message = useMessage();

// 账户余额
const balance = ref(0);
const balanceLoading = ref(false);

// 资源包列表
const packages = ref<Api.Package.PackageItem[]>([]);
const packagesLoading = ref(false);

// 购买相关
const showPurchaseModal = ref(false);
const selectedPackage = ref<Api.Package.PackageItem | null>(null);
const purchaseLoading = ref(false);
const purchasingPackageId = ref<number | null>(null);

// 购买记录
const purchaseRecords = ref<Api.Package.PackagePurchaseRecord[]>([]);
const recordsLoading = ref(false);
const recordsPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0
});
const recordsFilter = ref<{ package_id?: number }>({});

// 购买记录表格列定义
const recordColumns = [
  {
    title: '订单号',
    key: 'order_id',
    width: 120,
    ellipsis: { tooltip: true }
  },
  {
    title: '资源包名称',
    key: 'package_name',
    width: 120
  },
  {
    title: '总调用次数',
    key: 'total_quota',
    width: 100,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      const amount = row.total_quota || 0;
      return `${amount.toLocaleString()}次`;
    }
  },
  {
    title: '已使用',
    key: 'used_quota',
    width: 100,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      const used = row.used_quota || 0;
      return `${used.toLocaleString()}次`;
    }
  },
  {
    title: '剩余次数',
    key: 'remaining_quota',
    width: 100,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      const remaining = row.remaining_quota || 0;
      return `${remaining.toLocaleString()}次`;
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      const statusMap: Record<number, { text: string; type: 'success' | 'warning' | 'default' }> = {
        1: { text: '有效', type: 'success' },
        2: { text: '已过期', type: 'warning' },
        3: { text: '已用完', type: 'default' }
      };
      const config = statusMap[row.status] || { text: '未知', type: 'default' };
      return h(NTag, { type: config.type, size: 'small' }, () => config.text);
    }
  },
  {
    title: '购买时间',
    key: 'created_at',
    width: 160,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      if (!row.created_at) return '-';
      try {
        return new Date(row.created_at).toLocaleString();
      } catch {
        return '-';
      }
    }
  },
  {
    title: '到期时间',
    key: 'expires_at',
    width: 160,
    render: (row: Api.Package.PackagePurchaseRecord) => {
      if (!row.expires_at) return '-';
      try {
        return new Date(row.expires_at).toLocaleString();
      } catch {
        return '-';
      }
    }
  }
];

// 资源包筛选选项
const packageFilterOptions = computed(() => {
  const options: Array<{ label: string; value: number | undefined }> = [{ label: '全部资源包', value: undefined }];
  packages.value.forEach(pkg => {
    options.push({ label: pkg.package_name, value: pkg.id });
  });
  return options;
});

// 获取账户余额
const getAccountBalance = async () => {
  balanceLoading.value = true;
  try {
    const { data: balanceData, error } = await fetchAccountBalance();
    if (!error && balanceData) {
      balance.value = typeof balanceData.account_balance === 'number' ? balanceData.account_balance : 0;
    }
  } catch (error) {
    console.error('获取账户余额失败:', error);
    balance.value = 0;
  } finally {
    balanceLoading.value = false;
  }
};

// 获取资源包列表
const getPackageList = async () => {
  packagesLoading.value = true;
  try {
    const { data: packageData, error } = await fetchPackageList();

    if (!error && packageData) {
      packages.value = Array.isArray(packageData) ? packageData : [];
      // 按排序顺序排序
      packages.value.sort((a, b) => a.sort_order - b.sort_order);
    }
  } catch (error) {
    console.error('获取资源包列表失败:', error);
    packages.value = [];
  } finally {
    packagesLoading.value = false;
  }
};

// 获取购买记录
const getPurchaseRecords = async (page = 1) => {
  recordsLoading.value = true;
  try {
    const params: Api.Package.PackagePurchaseRecordsParams = {
      page,
      page_size: recordsPagination.value.pageSize
    };

    if (recordsFilter.value.package_id) {
      params.package_id = recordsFilter.value.package_id;
    }

    console.log('请求购买记录参数:', params);
    const { data: recordsData, error } = await fetchPackagePurchaseRecords(params);
    console.log('购买记录响应数据:', JSON.stringify(recordsData));
    console.log('购买记录错误信息:', error);

    if (!error && recordsData) {
      // 确保数据结构正确
      if (recordsData.packages && Array.isArray(recordsData.packages)) {
        purchaseRecords.value = recordsData.packages;
        recordsPagination.value.itemCount = typeof recordsData.total === 'number' ? recordsData.total : 0;
        recordsPagination.value.page = page;
        console.log('成功获取购买记录:', purchaseRecords.value.length, '条');
        console.log('购买记录详细数据:', purchaseRecords.value);
        // 检查每条记录的数据结构
        purchaseRecords.value.forEach((record: Api.Package.PackagePurchaseRecord, index) => {
          console.log(`记录 ${index + 1}:`, {
            id: record.id,
            order_id: record.order_id,
            package_name: record.package_name,
            total_quota: record.total_quota,
            used_quota: record.used_quota,
            remaining_quota: record.remaining_quota,
            status: record.status,
            created_at: record.created_at,
            expires_at: record.expires_at,
            used_up_at: record.used_up_at
          });
        });
      } else {
        console.warn('购买记录数据格式异常:', recordsData);
        purchaseRecords.value = [];
        recordsPagination.value.itemCount = 0;
      }
    } else {
      console.error('获取购买记录失败:', error);
      purchaseRecords.value = [];
      recordsPagination.value.itemCount = 0;
      if (error) {
        message.error(`获取购买记录失败: ${error.message || '未知错误'}`);
      }
    }
  } catch (error) {
    console.error('获取购买记录异常:', error);
    purchaseRecords.value = [];
    recordsPagination.value.itemCount = 0;
    message.error('获取购买记录失败，请稍后重试');
  } finally {
    recordsLoading.value = false;
  }
};

// 处理购买资源包
const handlePurchasePackage = (pkg: Api.Package.PackageItem) => {
  if (balance.value < pkg.price) {
    message.error('余额不足，请先充值');
    return;
  }
  selectedPackage.value = pkg;
  showPurchaseModal.value = true;
};

// 确认购买
const confirmPurchase = async () => {
  if (!selectedPackage.value) return;

  purchaseLoading.value = true;
  purchasingPackageId.value = selectedPackage.value.id;

  try {
    const { data: purchaseData, error } = await fetchPurchasePackage({
      package_id: selectedPackage.value.id,
      payment_method: 'balance',
    });

    if (!error && purchaseData) {
      console.log("purchaseData======="+JSON.stringify(purchaseData));

      message.success('购买成功！');
      showPurchaseModal.value = false;

      // 更新余额
      balance.value = purchaseData.account_balance;

      // 刷新购买记录
      getPurchaseRecords();
    } else {
      message.error('购买失败，请重试');
    }
  } catch (error) {
    console.error('购买资源包失败:', error);
    message.error('购买失败，请重试');
  } finally {
    purchaseLoading.value = false;
    purchasingPackageId.value = null;
  }
};

// 处理记录分页变化
const handleRecordsPageChange = (page: number) => {
  getPurchaseRecords(page);
};

// 处理筛选变化
const handleFilterChange = () => {
  getPurchaseRecords(1);
};

// 页面加载时获取数据
onMounted(() => {
  getAccountBalance();
  getPackageList();
  getPurchaseRecords();
});
</script>

<style scoped>
.card-wrapper {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.package-card {
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.package-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.text-primary {
  color: #3b82f6;
}
</style>
