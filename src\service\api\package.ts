import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * 获取资源包列表
 */
export function fetchPackageList() {
  return request<Api.Package.PackageListResponse>({
    url: '/api/v1/billing/resource-packages/templates',
    method: 'get'
  });
}

/**
 * 购买资源包
 * @param data 购买资源包请求数据
 */
export function fetchPurchasePackage(data: Api.Package.PurchasePackageRequest) {
  return request<Api.Package.PurchasePackageResponse>({
    url: '/api/v1/billing/resource-packages/purchase',
    method: 'post',
    data
  });
}

/**
 * 获取资源包购买记录
 * @param params 查询参数
 */
export function fetchPackagePurchaseRecords(params: Api.Package.PackagePurchaseRecordsParams) {
  return request<Api.Package.PackagePurchaseRecordsResponse>({
    url: '/api/v1/billing/resource-packages',
    method: 'get',
    params
  });
}
