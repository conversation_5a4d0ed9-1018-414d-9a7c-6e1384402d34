import { computed, ref } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { REG_CODE_CAPTCHA, REG_PHONE } from '@/constants/reg';
import { $t } from '@/locales';
import { fetchSendRegisterSms, fetchSendResetPasswordSms } from '@/service/api';

export function useCaptcha(type: string = 'register') {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(120);

  // // 存储重置密码的token
  // const resetToken = ref<string>('');

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      window.$message?.error?.($t('form.phone.required'));

      return false;
    }

    if (!REG_PHONE.test(phone)) {
      window.$message?.error?.($t('form.phone.invalid'));

      return false;
    }

    return true;
  }

  function isCaptchaCodeValid(code: string) {
    if (code.trim() === '') {
      window.$message?.error?.($t('form.captcha.required'));

      return false;
    }

    if (!REG_CODE_CAPTCHA.test(code)) {
      window.$message?.error?.($t('form.captcha.invalid'));

      return false;
    }

    return true;
  }

  async function getCaptcha(phone: string,captchaId: string, captchaCode: string) {
    const valid = isPhoneValid(phone);
    const valid1 = isCaptchaCodeValid(captchaCode);

    if (!valid || !valid1 || loading.value) {
      return;
    }

    startLoading();
    // request
    if(type === 'reset_password'){
      //发送重置密码验证码
      try {
        const { data: resetSmsResponse, error } = await fetchSendResetPasswordSms(phone, type, captchaId, captchaCode);
        if (error) {
          // API调用失败，错误消息已经通过request拦截器显示了
          console.error('验证码发送失败:', error);
          endLoading();
          return;
        } else {
          console.log("验证码====", resetSmsResponse.code);
          console.log("重置密码token====", resetSmsResponse.token);
          // // 存储重置密码的token
          // resetToken.value = resetSmsResponse.token;
          window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));
          start(); // 只有成功时才启动倒计时
        }
      } catch (error) {
        // 捕获异常
        console.error('验证码发送异常:', error);
        endLoading();
        return;
      }
    }else{
      //发送注册验证码
      try {
        const { data: registerSmsResponse, error } = await fetchSendRegisterSms(phone, type, captchaId, captchaCode);
        if (error) {
          // API调用失败，错误消息已经通过request拦截器显示了
          console.error('验证码发送失败:', error);
          endLoading();
          return;
        } else {
          console.log("验证码====", registerSmsResponse.code);
          window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));
          start(); // 只有成功时才启动倒计时
        }
      } catch (error) {
        // 捕获异常
        console.error('验证码发送异常:', error);
        endLoading();
        return;
      }
    }

    endLoading();
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha,
    //resetToken
  };
}
