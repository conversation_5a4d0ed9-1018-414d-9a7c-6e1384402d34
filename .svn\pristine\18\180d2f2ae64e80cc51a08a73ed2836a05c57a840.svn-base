hoistPattern:
  - '*'
hoistedDependencies:
  '@alova/mock@2.0.16(alova@3.3.0)':
    '@alova/mock': public
  '@alova/shared@1.3.0':
    '@alova/shared': public
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@antfu/eslint-define-config@1.23.0-2':
    '@antfu/eslint-define-config': public
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': public
  '@antfu/utils@8.1.1':
    '@antfu/utils': public
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': public
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': public
  '@babel/core@7.28.0':
    '@babel/core': public
  '@babel/generator@7.28.0':
    '@babel/generator': public
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': public
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': public
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': public
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': public
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': public
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': public
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': public
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': public
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': public
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': public
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.27.6':
    '@babel/helpers': public
  '@babel/parser@7.28.0':
    '@babel/parser': public
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-decorators': public
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': public
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': public
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': public
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': public
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': public
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': public
  '@babel/template@7.27.2':
    '@babel/template': public
  '@babel/traverse@7.28.0':
    '@babel/traverse': public
  '@babel/types@7.28.1':
    '@babel/types': public
  '@better-scroll/shared-utils@2.5.1':
    '@better-scroll/shared-utils': public
  '@css-render/plugin-bem@0.15.14(css-render@0.15.14)':
    '@css-render/plugin-bem': public
  '@css-render/vue3-ssr@0.15.14(vue@3.5.16(typescript@5.8.3))':
    '@css-render/vue3-ssr': public
  '@elegant-router/core@0.3.8':
    '@elegant-router/core': public
  '@emnapi/core@1.4.5':
    '@emnapi/core': public
  '@emnapi/runtime@1.4.5':
    '@emnapi/runtime': public
  '@emnapi/wasi-threads@1.0.4':
    '@emnapi/wasi-threads': public
  '@emotion/hash@0.8.0':
    '@emotion/hash': public
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': public
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': public
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': public
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': public
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': public
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': public
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': public
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': public
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': public
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': public
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': public
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': public
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': public
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': public
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': public
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': public
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': public
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': public
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': public
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': public
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': public
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': public
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': public
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': public
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': public
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.7.0(eslint@9.28.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/compat@1.3.1(eslint@9.28.0(jiti@2.4.2))':
    '@eslint/compat': public
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': public
  '@eslint/core@0.14.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/js@9.26.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.4':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': public
  '@humanfs/node@0.16.6':
    '@humanfs/node': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': public
  '@iconify/types@2.0.0':
    '@iconify/types': public
  '@iconify/utils@2.3.0':
    '@iconify/utils': public
  '@intlify/core-base@11.1.5':
    '@intlify/core-base': public
  '@intlify/message-compiler@11.1.5':
    '@intlify/message-compiler': public
  '@intlify/shared@11.1.5':
    '@intlify/shared': public
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': public
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': public
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': public
  '@napi-rs/wasm-runtime@0.2.12':
    '@napi-rs/wasm-runtime': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': public
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': public
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': public
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': public
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': public
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': public
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': public
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': public
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': public
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': public
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': public
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': public
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': public
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@pkgr/core@0.2.9':
    '@pkgr/core': public
  '@polka/url@1.0.0-next.29':
    '@polka/url': public
  '@quansync/fs@0.1.3':
    '@quansync/fs': public
  '@rolldown/pluginutils@1.0.0-beta.29':
    '@rolldown/pluginutils': public
  '@rollup/pluginutils@5.2.0(rollup@4.45.1)':
    '@rollup/pluginutils': public
  '@rollup/rollup-android-arm-eabi@4.45.1':
    '@rollup/rollup-android-arm-eabi': public
  '@rollup/rollup-android-arm64@4.45.1':
    '@rollup/rollup-android-arm64': public
  '@rollup/rollup-darwin-arm64@4.45.1':
    '@rollup/rollup-darwin-arm64': public
  '@rollup/rollup-darwin-x64@4.45.1':
    '@rollup/rollup-darwin-x64': public
  '@rollup/rollup-freebsd-arm64@4.45.1':
    '@rollup/rollup-freebsd-arm64': public
  '@rollup/rollup-freebsd-x64@4.45.1':
    '@rollup/rollup-freebsd-x64': public
  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    '@rollup/rollup-linux-arm-gnueabihf': public
  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    '@rollup/rollup-linux-arm-musleabihf': public
  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    '@rollup/rollup-linux-arm64-gnu': public
  '@rollup/rollup-linux-arm64-musl@4.45.1':
    '@rollup/rollup-linux-arm64-musl': public
  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    '@rollup/rollup-linux-loongarch64-gnu': public
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    '@rollup/rollup-linux-powerpc64le-gnu': public
  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    '@rollup/rollup-linux-riscv64-gnu': public
  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    '@rollup/rollup-linux-riscv64-musl': public
  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    '@rollup/rollup-linux-s390x-gnu': public
  '@rollup/rollup-linux-x64-gnu@4.45.1':
    '@rollup/rollup-linux-x64-gnu': public
  '@rollup/rollup-linux-x64-musl@4.45.1':
    '@rollup/rollup-linux-x64-musl': public
  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    '@rollup/rollup-win32-arm64-msvc': public
  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    '@rollup/rollup-win32-ia32-msvc': public
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': public
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': public
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': public
  '@soybeanjs/changelog@0.3.24(@unocss/eslint-config@66.1.4(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3))(eslint-plugin-vue@10.2.0(eslint@9.28.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.28.0(jiti@2.4.2))))(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)(vue-eslint-parser@10.1.3(eslint@9.28.0(jiti@2.4.2)))':
    '@soybeanjs/changelog': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': public
  '@tybys/wasm-util@0.10.0':
    '@tybys/wasm-util': public
  '@types/crypto-js@4.2.2':
    '@types/crypto-js': public
  '@types/debug@4.1.12':
    '@types/debug': public
  '@types/estree@1.0.8':
    '@types/estree': public
  '@types/json-schema@7.0.15':
    '@types/json-schema': public
  '@types/katex@0.16.7':
    '@types/katex': public
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': public
  '@types/lodash@4.17.20':
    '@types/lodash': public
  '@types/mdast@4.0.4':
    '@types/mdast': public
  '@types/ms@2.1.0':
    '@types/ms': public
  '@types/qs@6.14.0':
    '@types/qs': public
  '@types/sortablejs@1.15.8':
    '@types/sortablejs': public
  '@types/svgo@2.6.4':
    '@types/svgo': public
  '@types/unist@3.0.3':
    '@types/unist': public
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': public
  '@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.32.1(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.32.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.32.1(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.32.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.32.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.32.1(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.32.1':
    '@typescript-eslint/visitor-keys': public
  '@unocss/config@66.1.4':
    '@unocss/config': public
  '@unocss/core@66.1.4':
    '@unocss/core': public
  '@unocss/eslint-plugin@66.1.4(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3)':
    '@unocss/eslint-plugin': public
  '@unocss/extractor-arbitrary-variants@66.1.4':
    '@unocss/extractor-arbitrary-variants': public
  '@unocss/inspector@66.1.4(vue@3.5.16(typescript@5.8.3))':
    '@unocss/inspector': public
  '@unocss/preset-mini@66.1.4':
    '@unocss/preset-mini': public
  '@unocss/preset-wind3@66.1.4':
    '@unocss/preset-wind3': public
  '@unocss/rule-utils@66.1.4':
    '@unocss/rule-utils': public
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': public
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': public
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': public
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': public
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': public
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': public
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': public
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': public
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': public
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': public
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': public
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': public
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': public
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': public
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': public
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': public
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': public
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': public
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': public
  '@volar/language-core@2.4.20':
    '@volar/language-core': public
  '@volar/source-map@2.4.20':
    '@volar/source-map': public
  '@volar/typescript@2.4.20':
    '@volar/typescript': public
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': public
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-jsx': public
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-resolve-type': public
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': public
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': public
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': public
  '@vue/devtools-core@7.7.7(vite@6.3.5(@types/node@22.15.30)(jiti@2.4.2)(sass@1.89.1)(tsx@4.19.4)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vue/devtools-core': public
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': public
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': public
  '@vue/language-core@2.2.10(typescript@5.8.3)':
    '@vue/language-core': public
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.8.3))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.16':
    '@vue/shared': public
  '@vueuse/metadata@13.3.0':
    '@vueuse/metadata': public
  '@vueuse/shared@13.3.0(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/shared': public
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: public
  acorn@8.15.0:
    acorn: public
  ajv@6.12.6:
    ajv: public
  alien-signals@1.0.13:
    alien-signals: public
  alova@3.3.0:
    alova: public
  ansi-colors@4.1.3:
    ansi-colors: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  ansis@4.1.0:
    ansis: public
  anymatch@3.1.3:
    anymatch: public
  argparse@2.0.1:
    argparse: public
  args-tokenizer@0.3.0:
    args-tokenizer: public
  arr-diff@4.0.0:
    arr-diff: public
  arr-flatten@1.1.0:
    arr-flatten: public
  arr-union@3.1.0:
    arr-union: public
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: public
  array-unique@0.3.2:
    array-unique: public
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: public
  assign-symbols@1.0.0:
    assign-symbols: public
  ast-types@0.16.1:
    ast-types: public
  async-function@1.0.0:
    async-function: public
  async-validator@4.2.5:
    async-validator: public
  asynckit@0.4.0:
    asynckit: public
  atob@2.1.2:
    atob: public
  available-typed-arrays@1.0.7:
    available-typed-arrays: public
  axios-retry@4.5.0(axios@1.9.0):
    axios-retry: public
  axios@1.9.0:
    axios: public
  balanced-match@1.0.2:
    balanced-match: public
  base@0.11.2:
    base: public
  big.js@5.2.2:
    big.js: public
  binary-extensions@2.3.0:
    binary-extensions: public
  binary-searching@2.0.5:
    binary-searching: public
  birpc@2.5.0:
    birpc: public
  bluebird@3.7.2:
    bluebird: public
  boolbase@1.0.0:
    boolbase: public
  brace-expansion@1.1.12:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browserslist@4.25.1:
    browserslist: public
  builtin-modules@5.0.0:
    builtin-modules: public
  bumpp@10.1.1:
    bumpp: public
  bundle-name@4.1.0:
    bundle-name: public
  c12@3.0.4:
    c12: public
  cac@6.7.14:
    cac: public
  cache-base@1.0.1:
    cache-base: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  call-bind@1.0.8:
    call-bind: public
  call-bound@1.0.4:
    call-bound: public
  callsites@3.1.0:
    callsites: public
  camelcase@6.3.0:
    camelcase: public
  caniuse-lite@1.0.30001727:
    caniuse-lite: public
  chalk@4.1.2:
    chalk: public
  character-entities@2.0.2:
    character-entities: public
  chokidar@3.6.0:
    chokidar: public
  ci-info@4.3.0:
    ci-info: public
  citty@0.1.6:
    citty: public
  class-utils@0.3.6:
    class-utils: public
  clean-regexp@1.0.0:
    clean-regexp: public
  cli-progress@3.12.0:
    cli-progress: public
  cliui@8.0.1:
    cliui: public
  clone@2.1.2:
    clone: public
  collection-visit@1.0.0:
    collection-visit: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  colord@2.9.3:
    colord: public
  colorette@2.0.20:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@7.2.0:
    commander: public
  comment-parser@1.4.1:
    comment-parser: public
  component-emitter@1.3.1:
    component-emitter: public
  concat-map@0.0.1:
    concat-map: public
  confbox@0.2.2:
    confbox: public
  convert-gitmoji@0.1.5:
    convert-gitmoji: public
  convert-source-map@2.0.0:
    convert-source-map: public
  copy-anything@3.0.5:
    copy-anything: public
  copy-descriptor@0.1.1:
    copy-descriptor: public
  core-js-compat@3.44.0:
    core-js-compat: public
  cors@2.8.5:
    cors: public
  cross-spawn@7.0.6:
    cross-spawn: public
  crypto-js@4.2.0:
    crypto-js: public
  css-render@0.15.14:
    css-render: public
  css-select@4.3.0:
    css-select: public
  css-tree@3.1.0:
    css-tree: public
  css-what@6.2.2:
    css-what: public
  cssesc@3.0.0:
    cssesc: public
  csso@4.2.0:
    csso: public
  csstype@3.1.3:
    csstype: public
  data-view-buffer@1.0.2:
    data-view-buffer: public
  data-view-byte-length@1.0.2:
    data-view-byte-length: public
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: public
  date-fns-tz@3.2.0(date-fns@3.6.0):
    date-fns-tz: public
  date-fns@3.6.0:
    date-fns: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.4.1:
    debug: public
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: public
  decode-uri-component@0.2.2:
    decode-uri-component: public
  deep-is@0.1.4:
    deep-is: public
  default-browser-id@5.0.0:
    default-browser-id: public
  default-browser@5.2.1:
    default-browser: public
  define-data-property@1.1.4:
    define-data-property: public
  define-lazy-prop@3.0.0:
    define-lazy-prop: public
  define-properties@1.2.1:
    define-properties: public
  define-property@1.0.0:
    define-property: public
  delayed-stream@1.0.0:
    delayed-stream: public
  delegate@3.2.0:
    delegate: public
  dequal@2.0.3:
    dequal: public
  destr@2.0.5:
    destr: public
  detect-libc@1.0.3:
    detect-libc: public
  devlop@1.1.0:
    devlop: public
  dom-serializer@1.4.1:
    dom-serializer: public
  domelementtype@2.3.0:
    domelementtype: public
  domhandler@4.3.1:
    domhandler: public
  domutils@2.8.0:
    domutils: public
  dotenv@16.6.1:
    dotenv: public
  dunder-proto@1.0.1:
    dunder-proto: public
  duplexer@0.1.2:
    duplexer: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  electron-to-chromium@1.5.189:
    electron-to-chromium: public
  emoji-regex@8.0.0:
    emoji-regex: public
  emojis-list@3.0.0:
    emojis-list: public
  enhanced-resolve@5.18.2:
    enhanced-resolve: public
  enquirer@2.4.1:
    enquirer: public
  entities@4.5.0:
    entities: public
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: public
  es-abstract@1.24.0:
    es-abstract: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: public
  es-to-primitive@1.3.0:
    es-to-primitive: public
  esbuild@0.25.8:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-compat-utils@0.5.1(eslint@9.28.0(jiti@2.4.2)):
    eslint-compat-utils: public
  eslint-config-flat-gitignore@2.1.0(eslint@9.28.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: public
  eslint-config-prettier@10.1.5(eslint@9.28.0(jiti@2.4.2)):
    eslint-config-prettier: public
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-parser-plain@0.1.1:
    eslint-parser-plain: public
  eslint-plugin-es-x@7.8.0(eslint@9.28.0(jiti@2.4.2)):
    eslint-plugin-es-x: public
  eslint-plugin-import-x@4.11.1(eslint@9.28.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-import-x: public
  eslint-plugin-n@17.18.0(eslint@9.28.0(jiti@2.4.2)):
    eslint-plugin-n: public
  eslint-plugin-prettier@5.4.0(eslint-config-prettier@10.1.5(eslint@9.28.0(jiti@2.4.2)))(eslint@9.28.0(jiti@2.4.2))(prettier@3.5.3):
    eslint-plugin-prettier: public
  eslint-plugin-unicorn@59.0.1(eslint@9.28.0(jiti@2.4.2)):
    eslint-plugin-unicorn: public
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: public
  esprima@4.0.1:
    esprima: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  etag@1.8.1:
    etag: public
  evtd@0.2.4:
    evtd: public
  execa@9.6.0:
    execa: public
  expand-brackets@2.1.4:
    expand-brackets: public
  exsolve@1.0.7:
    exsolve: public
  extend-shallow@2.0.1:
    extend-shallow: public
  extglob@2.0.4:
    extglob: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-diff@1.3.0:
    fast-diff: public
  fast-glob@3.3.2:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fastq@1.19.1:
    fastq: public
  fdir@6.4.6(picomatch@4.0.3):
    fdir: public
  figures@6.1.0:
    figures: public
  file-entry-cache@8.0.0:
    file-entry-cache: public
  fill-range@4.0.0:
    fill-range: public
  find-up-simple@1.0.1:
    find-up-simple: public
  find-up@5.0.0:
    find-up: public
  flat-cache@4.0.1:
    flat-cache: public
  flatted@3.3.3:
    flatted: public
  follow-redirects@1.15.9:
    follow-redirects: public
  for-each@0.3.5:
    for-each: public
  for-in@1.0.2:
    for-in: public
  foreground-child@3.3.1:
    foreground-child: public
  form-data@4.0.4:
    form-data: public
  fragment-cache@0.2.1:
    fragment-cache: public
  fs-extra@10.1.0:
    fs-extra: public
  fsevents@2.3.3:
    fsevents: public
  function-bind@1.1.2:
    function-bind: public
  function.prototype.name@1.1.8:
    function.prototype.name: public
  functions-have-names@1.2.3:
    functions-have-names: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@9.0.1:
    get-stream: public
  get-symbol-description@1.1.0:
    get-symbol-description: public
  get-tsconfig@4.10.1:
    get-tsconfig: public
  get-value@2.0.6:
    get-value: public
  giget@2.0.0:
    giget: public
  glob-parent@6.0.2:
    glob-parent: public
  glob@11.0.3:
    glob: public
  globals@16.1.0:
    globals: public
  globalthis@1.0.4:
    globalthis: public
  good-listener@1.2.2:
    good-listener: public
  gopd@1.2.0:
    gopd: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  gzip-size@6.0.0:
    gzip-size: public
  has-ansi@2.0.0:
    has-ansi: public
  has-bigints@1.1.0:
    has-bigints: public
  has-flag@4.0.0:
    has-flag: public
  has-property-descriptors@1.0.2:
    has-property-descriptors: public
  has-proto@1.2.0:
    has-proto: public
  has-symbols@1.1.0:
    has-symbols: public
  has-tostringtag@1.0.2:
    has-tostringtag: public
  has-value@1.0.0:
    has-value: public
  has-values@1.0.0:
    has-values: public
  hasown@2.0.2:
    hasown: public
  he@1.2.0:
    he: public
  highlight.js@11.11.1:
    highlight.js: public
  hookable@5.5.3:
    hookable: public
  htmlparser2@3.10.1:
    htmlparser2: public
  human-signals@8.0.1:
    human-signals: public
  icss-replace-symbols@1.1.0:
    icss-replace-symbols: public
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: public
  ignore@5.3.2:
    ignore: public
  image-size@0.5.5:
    image-size: public
  immediate@3.0.6:
    immediate: public
  immutable@5.1.3:
    immutable: public
  import-fresh@3.3.1:
    import-fresh: public
  imurmurhash@0.1.4:
    imurmurhash: public
  indent-string@5.0.0:
    indent-string: public
  inherits@2.0.4:
    inherits: public
  internal-slot@1.1.0:
    internal-slot: public
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: public
  is-array-buffer@3.0.5:
    is-array-buffer: public
  is-async-function@2.1.1:
    is-async-function: public
  is-bigint@1.1.0:
    is-bigint: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-boolean-object@1.2.2:
    is-boolean-object: public
  is-buffer@1.1.6:
    is-buffer: public
  is-builtin-module@5.0.0:
    is-builtin-module: public
  is-callable@1.2.7:
    is-callable: public
  is-core-module@2.16.1:
    is-core-module: public
  is-data-descriptor@1.0.1:
    is-data-descriptor: public
  is-data-view@1.0.2:
    is-data-view: public
  is-date-object@1.1.0:
    is-date-object: public
  is-descriptor@1.0.3:
    is-descriptor: public
  is-docker@3.0.0:
    is-docker: public
  is-extendable@0.1.1:
    is-extendable: public
  is-extglob@2.1.1:
    is-extglob: public
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-generator-function@1.1.0:
    is-generator-function: public
  is-glob@4.0.3:
    is-glob: public
  is-inside-container@1.0.0:
    is-inside-container: public
  is-map@2.0.3:
    is-map: public
  is-negative-zero@2.0.3:
    is-negative-zero: public
  is-number-object@1.1.1:
    is-number-object: public
  is-number@3.0.0:
    is-number: public
  is-plain-obj@4.1.0:
    is-plain-obj: public
  is-plain-object@2.0.4:
    is-plain-object: public
  is-regex@1.2.1:
    is-regex: public
  is-retry-allowed@2.2.0:
    is-retry-allowed: public
  is-set@2.0.3:
    is-set: public
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: public
  is-stream@4.0.1:
    is-stream: public
  is-string@1.1.1:
    is-string: public
  is-symbol@1.1.1:
    is-symbol: public
  is-there@4.5.2:
    is-there: public
  is-typed-array@1.1.15:
    is-typed-array: public
  is-unicode-supported@2.1.0:
    is-unicode-supported: public
  is-weakmap@2.0.2:
    is-weakmap: public
  is-weakref@1.1.1:
    is-weakref: public
  is-weakset@2.0.4:
    is-weakset: public
  is-what@4.1.16:
    is-what: public
  is-windows@1.0.2:
    is-windows: public
  is-wsl@3.1.0:
    is-wsl: public
  isarray@2.0.5:
    isarray: public
  isexe@2.0.0:
    isexe: public
  isobject@3.0.1:
    isobject: public
  jackspeak@3.4.3:
    jackspeak: public
  jiti@2.4.2:
    jiti: public
  js-base64@2.6.4:
    js-base64: public
  js-tokens@4.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsesc@3.1.0:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  jsonc-parser@3.3.1:
    jsonc-parser: public
  jsonfile@6.1.0:
    jsonfile: public
  keyv@4.5.4:
    keyv: public
  kind-of@5.1.0:
    kind-of: public
  kleur@3.0.3:
    kleur: public
  klona@2.0.6:
    klona: public
  levn@0.4.1:
    levn: public
  lie@3.1.1:
    lie: public
  loader-utils@1.4.2:
    loader-utils: public
  local-pkg@1.1.1:
    local-pkg: public
  localforage@1.10.0:
    localforage: public
  locate-path@6.0.0:
    locate-path: public
  lodash-es@4.17.21:
    lodash-es: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash@4.17.21:
    lodash: public
  lru-cache@5.1.1:
    lru-cache: public
  magic-string@0.30.11:
    magic-string: public
  magicast@0.3.4:
    magicast: public
  map-cache@0.2.2:
    map-cache: public
  map-visit@1.0.0:
    map-visit: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: public
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: public
  mdn-data@2.12.2:
    mdn-data: public
  merge-options@1.0.1:
    merge-options: public
  merge2@1.4.1:
    merge2: public
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: public
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: public
  micromark-factory-label@2.0.1:
    micromark-factory-label: public
  micromark-factory-space@2.0.1:
    micromark-factory-space: public
  micromark-factory-title@2.0.1:
    micromark-factory-title: public
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: public
  micromark-util-character@2.1.1:
    micromark-util-character: public
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: public
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: public
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: public
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: public
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: public
  micromark-util-encode@2.0.1:
    micromark-util-encode: public
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: public
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: public
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: public
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: public
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: public
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: public
  micromark-util-types@2.0.2:
    micromark-util-types: public
  micromark@4.0.2:
    micromark: public
  micromatch@4.0.7:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  min-indent@1.0.1:
    min-indent: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass@7.1.2:
    minipass: public
  mitt@3.0.1:
    mitt: public
  mixin-deep@1.3.2:
    mixin-deep: public
  mkdirp@3.0.1:
    mkdirp: public
  mlly@1.7.4:
    mlly: public
  mrmime@2.0.1:
    mrmime: public
  ms@2.1.3:
    ms: public
  muggle-string@0.4.1:
    muggle-string: public
  nanoid@5.1.5:
    nanoid: public
  nanomatch@1.2.13:
    nanomatch: public
  napi-postinstall@0.3.2:
    napi-postinstall: public
  natural-compare@1.4.0:
    natural-compare: public
  node-addon-api@7.1.1:
    node-addon-api: public
  node-fetch-native@1.6.6:
    node-fetch-native: public
  node-releases@2.0.19:
    node-releases: public
  normalize-path@3.0.0:
    normalize-path: public
  npm-check-updates@18.0.1:
    npm-check-updates: public
  npm-run-path@6.0.0:
    npm-run-path: public
  nth-check@2.1.1:
    nth-check: public
  nypm@0.6.0:
    nypm: public
  object-assign@4.1.1:
    object-assign: public
  object-copy@0.1.0:
    object-copy: public
  object-inspect@1.13.4:
    object-inspect: public
  object-keys@1.1.1:
    object-keys: public
  object-visit@1.0.1:
    object-visit: public
  object.assign@4.1.7:
    object.assign: public
  object.pick@1.3.0:
    object.pick: public
  ofetch@1.4.1:
    ofetch: public
  ohash@2.0.11:
    ohash: public
  open@10.2.0:
    open: public
  optionator@0.9.4:
    optionator: public
  own-keys@1.0.1:
    own-keys: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  package-json-from-dist@1.0.1:
    package-json-from-dist: public
  package-manager-detector@1.3.0:
    package-manager-detector: public
  packages/alova:
    '@sa/alova': public
  packages/ofetch:
    '@sa/fetch': public
  parent-module@1.0.1:
    parent-module: public
  parse-ms@4.0.0:
    parse-ms: public
  pascalcase@0.1.1:
    pascalcase: public
  path-browserify@1.0.1:
    path-browserify: public
  path-exists@4.0.0:
    path-exists: public
  path-key@3.1.1:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@1.11.1:
    path-scurry: public
  pathe@1.1.2:
    pathe: public
  perfect-debounce@1.0.0:
    perfect-debounce: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.3:
    picomatch: public
  pkg-types@2.2.0:
    pkg-types: public
  pluralize@8.0.0:
    pluralize: public
  posix-character-classes@0.1.1:
    posix-character-classes: public
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: public
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: public
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: public
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: public
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: public
  postcss-prefix-selector@1.16.1(postcss@5.2.18):
    postcss-prefix-selector: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss-value-parser@4.2.0:
    postcss-value-parser: public
  postcss@8.5.6:
    postcss: public
  posthtml-parser@0.2.1:
    posthtml-parser: public
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: public
  posthtml-render@1.4.0:
    posthtml-render: public
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: public
  posthtml@0.9.2:
    posthtml: public
  prelude-ls@1.2.1:
    prelude-ls: public
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  prettier-plugin-jsdoc@1.3.2(prettier@3.5.3):
    prettier-plugin-jsdoc: public
  prettier-plugin-json-sort@0.0.2(prettier@3.5.3):
    prettier-plugin-json-sort: public
  prettier@3.3.3:
    prettier: public
  pretty-ms@9.2.0:
    pretty-ms: public
  progress@2.0.3:
    progress: public
  prompts@2.4.2:
    prompts: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  punycode@2.3.1:
    punycode: public
  qs@6.14.0:
    qs: public
  quansync@0.2.10:
    quansync: public
  query-string@4.3.4:
    query-string: public
  queue-microtask@1.2.3:
    queue-microtask: public
  rate-limiter-flexible@5.0.5:
    rate-limiter-flexible: public
  rc9@2.1.2:
    rc9: public
  rd@2.0.1:
    rd: public
  readable-stream@3.6.2:
    readable-stream: public
  readdirp@3.6.0:
    readdirp: public
  recast@0.23.9:
    recast: public
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: public
  regex-not@1.0.2:
    regex-not: public
  regexp-tree@0.1.27:
    regexp-tree: public
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: public
  regjsparser@0.12.0:
    regjsparser: public
  repeat-element@1.1.4:
    repeat-element: public
  repeat-string@1.6.1:
    repeat-string: public
  require-directory@2.1.1:
    require-directory: public
  resolve-from@4.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve-url@0.2.1:
    resolve-url: public
  resolve@1.22.10:
    resolve: public
  ret@0.1.15:
    ret: public
  reusify@1.1.0:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rimraf@6.0.1:
    rimraf: public
  rollup@4.45.1:
    rollup: public
  run-applescript@7.0.0:
    run-applescript: public
  run-parallel@1.2.0:
    run-parallel: public
  safe-array-concat@1.1.3:
    safe-array-concat: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safe-push-apply@1.0.0:
    safe-push-apply: public
  safe-regex-test@1.1.0:
    safe-regex-test: public
  safe-regex@1.1.0:
    safe-regex: public
  seemly@0.3.10:
    seemly: public
  select@1.1.2:
    select: public
  semver@7.6.2:
    semver: public
  set-function-length@1.2.2:
    set-function-length: public
  set-function-name@2.0.2:
    set-function-name: public
  set-proto@1.0.0:
    set-proto: public
  set-value@2.0.1:
    set-value: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  side-channel-list@1.0.0:
    side-channel-list: public
  side-channel-map@1.0.1:
    side-channel-map: public
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: public
  side-channel@1.1.0:
    side-channel: public
  signal-exit@4.1.0:
    signal-exit: public
  simplebar-core@1.3.2:
    simplebar-core: public
  simplebar-vue@2.4.1(vue@3.5.16(typescript@5.8.3)):
    simplebar-vue: public
  sirv@3.0.1:
    sirv: public
  sisteransi@1.0.5:
    sisteransi: public
  snapdragon-node@2.1.1:
    snapdragon-node: public
  snapdragon-util@3.0.1:
    snapdragon-util: public
  snapdragon@0.8.2:
    snapdragon: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-resolve@0.5.3:
    source-map-resolve: public
  source-map-url@0.4.1:
    source-map-url: public
  source-map@0.6.1:
    source-map: public
  speakingurl@14.0.1:
    speakingurl: public
  split-string@3.1.0:
    split-string: public
  stable-hash@0.0.5:
    stable-hash: public
  stable@0.1.8:
    stable: public
  static-extend@0.1.2:
    static-extend: public
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: public
  strict-uri-encode@1.1.0:
    strict-uri-encode: public
  string-width@4.2.3:
    string-width: public
    string-width-cjs: public
  string.prototype.trim@1.2.10:
    string.prototype.trim: public
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: public
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: public
  string_decoder@1.3.0:
    string_decoder: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-final-newline@4.0.0:
    strip-final-newline: public
  strip-indent@4.0.0:
    strip-indent: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  superjson@2.2.2:
    superjson: public
  supports-color@7.2.0:
    supports-color: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  svg-baker@1.7.0:
    svg-baker: public
  svgo@2.8.0:
    svgo: public
  synckit@0.9.3:
    synckit: public
  tapable@2.2.2:
    tapable: public
  tiny-emitter@2.1.0:
    tiny-emitter: public
  tiny-invariant@1.3.3:
    tiny-invariant: public
  tinyexec@1.0.1:
    tinyexec: public
  tinyglobby@0.2.14:
    tinyglobby: public
  to-object-path@0.3.0:
    to-object-path: public
  to-regex-range@2.1.1:
    to-regex-range: public
  to-regex@3.0.2:
    to-regex: public
  totalist@3.0.1:
    totalist: public
  traverse@0.6.11:
    traverse: public
  treemate@0.3.11:
    treemate: public
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: public
  tslib@2.3.0:
    tslib: public
  type-check@0.4.0:
    type-check: public
  typed-array-buffer@1.0.3:
    typed-array-buffer: public
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: public
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: public
  typed-array-length@1.0.7:
    typed-array-length: public
  typed-css-modules@0.9.1:
    typed-css-modules: public
  typedarray.prototype.slice@1.0.5:
    typedarray.prototype.slice: public
  ufo@1.6.1:
    ufo: public
  unbox-primitive@1.1.0:
    unbox-primitive: public
  unconfig@7.3.2:
    unconfig: public
  undici-types@6.21.0:
    undici-types: public
  unicorn-magic@0.3.0:
    unicorn-magic: public
  union-value@1.0.1:
    union-value: public
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: public
  universalify@2.0.1:
    universalify: public
  unplugin-utils@0.2.4:
    unplugin-utils: public
  unplugin@1.12.0:
    unplugin: public
  unrs-resolver@1.11.1:
    unrs-resolver: public
  unset-value@1.0.0:
    unset-value: public
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: public
  uri-js@4.4.1:
    uri-js: public
  urix@0.1.0:
    urix: public
  use@3.1.1:
    use: public
  util-deprecate@1.0.2:
    util-deprecate: public
  vary@1.1.2:
    vary: public
  vdirs@0.1.8(vue@3.5.16(typescript@5.8.3)):
    vdirs: public
  vite-hot-client@2.1.0(vite@6.3.5(@types/node@22.15.30)(jiti@2.4.2)(sass@1.89.1)(tsx@4.19.4)(yaml@2.8.0)):
    vite-hot-client: public
  vite-plugin-inspect@0.8.9(rollup@4.45.1)(vite@6.3.5(@types/node@22.15.30)(jiti@2.4.2)(sass@1.89.1)(tsx@4.19.4)(yaml@2.8.0)):
    vite-plugin-inspect: public
  vite-plugin-vue-inspector@5.3.2(vite@6.3.5(@types/node@22.15.30)(jiti@2.4.2)(sass@1.89.1)(tsx@4.19.4)(yaml@2.8.0)):
    vite-plugin-vue-inspector: public
  vooks@0.2.12(vue@3.5.16(typescript@5.8.3)):
    vooks: public
  vscode-uri@3.1.0:
    vscode-uri: public
  vue-demi@0.13.11(vue@3.5.16(typescript@5.8.3)):
    vue-demi: public
  vue-flow-layout@0.1.1(vue@3.5.16(typescript@5.8.3)):
    vue-flow-layout: public
  vueuc@0.4.64(vue@3.5.16(typescript@5.8.3)):
    vueuc: public
  webpack-sources@3.3.3:
    webpack-sources: public
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: public
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: public
  which-builtin-type@1.2.1:
    which-builtin-type: public
  which-collection@1.0.2:
    which-collection: public
  which-typed-array@1.1.19:
    which-typed-array: public
  which@2.0.2:
    which: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@7.0.0:
    wrap-ansi: public
    wrap-ansi-cjs: public
  wsl-utils@0.1.0:
    wsl-utils: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  y18n@5.0.8:
    y18n: public
  yallist@3.1.1:
    yallist: public
  yaml@2.8.0:
    yaml: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yocto-queue@0.1.0:
    yocto-queue: public
  yoctocolors@2.1.1:
    yoctocolors: public
  zrender@5.6.1:
    zrender: public
ignoredBuilds:
  - vue-demi
  - unrs-resolver
  - '@parcel/watcher'
  - esbuild
  - simple-git-hooks
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Tue, 22 Jul 2025 08:19:18 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\qingdao-xuefajianfen\soybean-admin-mainA\node_modules\.pnpm
virtualStoreDirMaxLength: 60
