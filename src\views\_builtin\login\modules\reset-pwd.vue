<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { fetchResetPassword } from '@/service/api';
import { $t } from '@/locales';
import ImageCaptcha from '@/components/common/image-captcha.vue';

defineOptions({
  name: 'ResetPwd'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha('reset_password');

interface FormModel {
  phone: string;
  code: string;
  password: string;
  confirmPassword: string;
  imageCaptchaCode: string;
}

const model: FormModel = reactive({
  phone: '',
  code: '',
  password: '',
  confirmPassword: '',
  imageCaptchaCode: ''
});

// 图形验证码相关
const imageCaptchaId = ref('');

type RuleRecord = Partial<Record<keyof FormModel, App.Global.FormRule[]>>;

const rules = computed<RuleRecord>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    phone: formRules.phone,
    code: formRules.code,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password),
    imageCaptchaCode: formRules.captcha
  };
});

async function handleSubmit() {
  await validate();

  // // 检查是否有重置密码的token
  // if (!resetToken.value) {
  //   window.$message?.error('请先获取验证码');
  //   return;
  // }

  try {
    // 调用重置密码API
    const { data: resetPasswordResponse, error } = await fetchResetPassword(
      model.phone,
      //resetToken.value,
      model.code,
      model.password
    );

    if (error) {
      // API调用失败，错误消息已经通过request拦截器显示了
      console.error('重置密码失败:', error);
      return;
    } else {
      console.log("重置密码成功====", JSON.stringify(resetPasswordResponse));
      window.$message?.success('密码重置成功，请使用新密码登录');

      // 重置密码成功后，跳转到登录页面
      setTimeout(() => {
        toggleLoginModule('pwd-login');
      }, 1000);
    }
  } catch (error) {
    // 捕获异常
    console.error('重置密码异常:', error);
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>
    <!-- 图形验证码 -->
    <NFormItem path="imageCaptchaCode">
      <ImageCaptcha
        v-model="model.imageCaptchaCode"
        @update:captcha-id="imageCaptchaId = $event"
      />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" :placeholder="$t('page.login.common.codePlaceholder')" />
        <NButton
          type="primary"
          ghost
          size="large"
          :disabled="isCounting || loading || !model.imageCaptchaCode"
          :loading="loading"
          @click="getCaptcha(model.phone, imageCaptchaId, model.imageCaptchaCode)"
        >
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
