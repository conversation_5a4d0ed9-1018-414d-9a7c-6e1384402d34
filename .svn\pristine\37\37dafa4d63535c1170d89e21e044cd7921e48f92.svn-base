#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/scripts/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules:/mnt/e/qingdao-xuefajianfen/node_modules:/mnt/e/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/scripts/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules:/mnt/e/qingdao-xuefajianfen/node_modules:/mnt/e/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/tsx" ]; then
  exec "$basedir/tsx"  "$basedir/../@sa/scripts/bin.ts" "$@"
else
  exec tsx  "$basedir/../@sa/scripts/bin.ts" "$@"
fi
