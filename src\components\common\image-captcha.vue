<template>
  <div class="image-captcha-container">
    <n-input-group>
      <n-input
        v-model:value="captcha"
        :placeholder="$t('page.login.common.imageCaptchaPlaceholder')"
        :maxlength="8"
        @input="handleInput"
      />
      <div class="captcha-image-wrapper">
        <img
          v-if="captchaUrl"
          :src="captchaUrl"
          alt="验证码"
          class="captcha-image"
          @click="refreshCaptcha"
          title="点击刷新验证码"
        />
        <n-button
          v-else
          :loading="loading"
          @click="refreshCaptcha"
        >
          {{ $t('page.login.common.getImageCaptcha') }}
        </n-button>
      </div>
    </n-input-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { fetchImageCaptcha } from '@/service/api';
import { $t } from '@/locales';

interface Props {
  modelValue: string;
  disabled?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'update:captchaId', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

const emit = defineEmits<Emits>();

const captcha = ref('');
const captchaUrl = ref('');
const captchaId = ref('');
const loading = ref(false);

const handleInput = (value: string) => {
  captcha.value = value;
  emit('update:modelValue', value);
};

// 获取图形验证码
const refreshCaptcha = async () => {
  loading.value = true;
  try {
    const { data:captchaData, error } = await fetchImageCaptcha();

    console.log("图形验证captchaData"+JSON.stringify(captchaData));

    if (!error && captchaData) {
      // 直接使用返回的base64字段，因为它已经包含了完整的data URL
      captchaUrl.value = captchaData.base64;
      captchaId.value = captchaData.id;
      emit('update:captchaId', captchaData.id);
      // 清空输入框
      captcha.value = '';
      emit('update:modelValue', '');
    }
  } catch (error) {
    console.error('获取图形验证码失败:', error);
    window.$message?.error('获取图形验证码失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取验证码
onMounted(() => {
  refreshCaptcha();
});
</script>

<style scoped>
.image-captcha-container {
  width: 100%;
}

.captcha-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-left: none;
  border-radius: 0 6px 6px 0;
  background-color: #fafafa;
  cursor: pointer;
}

.captcha-image {
  height: 38px;
  width: 100px;
  object-fit: cover;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
}

.captcha-image:hover {
  opacity: 0.8;
}
</style>
