<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="题库管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <div class="flex items-center gap-12px">
          <span class="text-sm text-gray-500">总数量: {{ pagination.total }}</span>
          <NButton type="primary" @click="handleAddQuestion">
            新增题库
          </NButton>
        </div>
      </template>
      
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>

    <!-- 新增/编辑题库模态框 -->
    <NModal v-model:show="showModal" preset="card" :title="modalTitle" class="w-700px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="题目" path="question">
          <NInput v-model:value="formModel.question" placeholder="请输入题目" />
        </NFormItem>
        
        <NFormItem label="科目" path="subject">
          <NInput v-model:value="formModel.subject" placeholder="请输入科目" />
        </NFormItem>
        
        <NFormItem label="图片" path="image">
          <NUpload
            v-model:file-list="formModel.imageList"
            :max="1"
            list-type="image-card"
            @change="handleImageChange"
          >
            <NButton>上传图片</NButton>
          </NUpload>
        </NFormItem>

        <NFormItem label="预览图">
          <div class="w-full">
            <div class="grid grid-cols-2 gap-12px">
              <NFormItem label="A" path="optionA">
                <NInput v-model:value="formModel.optionA" placeholder="选项A" />
              </NFormItem>
              <NFormItem label="B" path="optionB">
                <NInput v-model:value="formModel.optionB" placeholder="选项B" />
              </NFormItem>
              <NFormItem label="C" path="optionC">
                <NInput v-model:value="formModel.optionC" placeholder="选项C" />
              </NFormItem>
              <NFormItem label="D" path="optionD">
                <NInput v-model:value="formModel.optionD" placeholder="选项D" />
              </NFormItem>
              <NFormItem label="E" path="optionE">
                <NInput v-model:value="formModel.optionE" placeholder="选项E" />
              </NFormItem>
              <NFormItem label="F" path="optionF">
                <NInput v-model:value="formModel.optionF" placeholder="选项F" />
              </NFormItem>
            </div>
          </div>
        </NFormItem>

        <NFormItem label="答案" path="answer">
          <NSelect
            v-model:value="formModel.answer"
            :options="answerOptions"
            placeholder="请选择正确答案"
          />
        </NFormItem>

        <NFormItem label="解析" path="explanation">
          <NInput
            v-model:value="formModel.explanation"
            type="textarea"
            :rows="3"
            placeholder="请输入解析"
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSubmit">提交</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, onMounted } from 'vue';
import type { Ref } from 'vue';
import { NButton, NTag, NPopconfirm } from 'naive-ui';
import type { DataTableColumns, FormInst, FormRules, UploadFileInfo } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useLoading } from '@sa/hooks';
import { $t } from '@/locales';
import { formatDateTime } from '@/utils/common';
import { fetchQuestionData, fetchCreateQuestion, fetchDeleteQuestion } from '@/service/api';
import { Api } from '@/typings/api';

defineOptions({
  name: 'QuestionBank'
});

const appStore = useAppStore();
const { loading, startLoading, endLoading } = useLoading();

// 表格数据
const data = ref<Api.QuestionBank.QuestionItem[]>([]);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 25, 30],
  onChange: (page: number) => {
    pagination.page = page;
    getQuestionData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getQuestionData();
  }
});

// 模态框
const showModal = ref(false);
const modalTitle = ref('');
const formRef = ref<FormInst | null>(null);

// 表单数据
interface FormModel {
  question: string;
  subject: string;
  image: string;
  imageList: UploadFileInfo[];
  optionA: string;
  optionB: string;
  optionC: string;
  optionD: string;
  optionE: string;
  optionF: string;
  answer: string;
  explanation: string;
}

const formModel = reactive<FormModel>({
  question: '',
  subject: '',
  image: '',
  imageList: [],
  optionA: '',
  optionB: '',
  optionC: '',
  optionD: '',
  optionE: '',
  optionF: '',
  answer: '',
  explanation: ''
});

// 答案选项
const answerOptions = [
  { label: 'A', value: 'A' },
  { label: 'B', value: 'B' },
  { label: 'C', value: 'C' },
  { label: 'D', value: 'D' },
  { label: 'E', value: 'E' },
  { label: 'F', value: 'F' }
];

// 表单验证规则
const rules: FormRules = {
  question: [
    { required: true, message: '请输入题目', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入科目', trigger: 'blur' }
  ],
  optionA: [
    { required: true, message: '请输入选项A', trigger: 'blur' }
  ],
  optionB: [
    { required: true, message: '请输入选项B', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请选择正确答案', trigger: 'change' }
  ]
};

// 表格列定义
const columns = ref([
  {
    type: 'selection',
    disabled: () => false
  },
  {
    key: 'index',
    title: '序号',
    width: 80,
    align: 'center',
    render: (_, index) => {
      return (pagination.page - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    key: 'id',
    title: '题库ID',
    width: 100,
    align: 'center'
  },
  {
    key: 'question',
    title: '题目',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: 'image',
    title: '题目图片',
    width: 120,
    render: row => {
      if (row.image) {
        return <img src={row.image} alt="题目图片" class="w-60px h-40px object-cover rounded" />;
      }
      return <span class="text-gray-400">无图片</span>;
    }
  },
  {
    key: 'status',
    title: '目前状态',
    width: 100,
    render: row => {
      const statusMap: Record<number, { label: string; type: 'success' | 'warning' | 'error' }> = {
        1: { label: '启用', type: 'success' },
        0: { label: '禁用', type: 'error' }
      };
      const status = statusMap[row.status] || { label: '未知', type: 'error' };
      return <NTag type={status.type}>{status.label}</NTag>;
    }
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: 180,
    render: row => formatDateTime(row.created_at)
  },
  {
    key: 'actions',
    title: '操作',
    width: 100,
    align: 'center',
    render: row => (
      <NPopconfirm onPositiveClick={() => handleDelete(row)}>
        {{
          default: () => '确定删除这个题目吗？',
          trigger: () => (
            <NButton size="small" type="error">
              删除
            </NButton>
          )
        }}
      </NPopconfirm>
    )
  }
]) as Ref<DataTableColumns<Api.QuestionBank.QuestionItem>>;

// 移动端分页
const mobilePagination = computed(() => {
  if (appStore.isMobile) {
    return {
      ...pagination,
      pageSlot: 5
    };
  }
  return false;
});

// 获取题库数据
async function getQuestionData() {
  startLoading();
  try {
    // TODO: 调用API获取题库数据
    // const { data: questionResponse, error } = await fetchQuestionData({
    //   page: pagination.page,
    //   page_size: pagination.pageSize
    // });
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));
    const mockData = Array.from({ length: pagination.pageSize }, (_, index) => ({
      id: (pagination.page - 1) * pagination.pageSize + index + 1,
      question: `这是第${(pagination.page - 1) * pagination.pageSize + index + 1}道题目`,
      subject: '数学',
      image: index % 3 === 0 ? 'https://via.placeholder.com/60x40' : '',
      options: {
        A: '选项A',
        B: '选项B',
        C: '选项C',
        D: '选项D',
        E: '选项E',
        F: '选项F'
      },
      answer: 'A',
      explanation: '这是解析内容',
      status: Math.random() > 0.5 ? 1 : 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    data.value = mockData;
    pagination.total = 100; // 模拟总数
    
  } catch (error) {
    console.error('获取题库数据失败:', error);
    window.$message?.error('获取题库数据失败');
    data.value = [];
  } finally {
    endLoading();
  }
}

// 新增题库
function handleAddQuestion() {
  modalTitle.value = '新增题库';
  resetForm();
  showModal.value = true;
}

// 删除题库
async function handleDelete(row: Api.QuestionBank.QuestionItem) {
  startLoading();
  try {
    // TODO: 调用删除API
    // await fetchDeleteQuestion(row.id);
    
    // 模拟删除
    await new Promise(resolve => setTimeout(resolve, 500));
    
    window.$message?.success('删除成功');
    getQuestionData();
  } catch (error) {
    console.error('删除失败:', error);
    window.$message?.error('删除失败');
  } finally {
    endLoading();
  }
}

// 图片上传处理
function handleImageChange({ fileList }: { fileList: UploadFileInfo[] }) {
  formModel.imageList = fileList;
  if (fileList.length > 0 && fileList[0].url) {
    formModel.image = fileList[0].url;
  } else {
    formModel.image = '';
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    startLoading();
    
    // TODO: 调用API提交数据
    // const submitData = {
    //   question: formModel.question,
    //   subject: formModel.subject,
    //   image: formModel.image,
    //   options: {
    //     A: formModel.optionA,
    //     B: formModel.optionB,
    //     C: formModel.optionC,
    //     D: formModel.optionD,
    //     E: formModel.optionE,
    //     F: formModel.optionF
    //   },
    //   answer: formModel.answer,
    //   explanation: formModel.explanation
    // };
    // await fetchCreateQuestion(submitData);
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 500));
    
    window.$message?.success('提交成功');
    showModal.value = false;
    getQuestionData();
  } catch (error) {
    console.error('提交失败:', error);
    window.$message?.error('提交失败');
  } finally {
    endLoading();
  }
}

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    question: '',
    subject: '',
    image: '',
    imageList: [],
    optionA: '',
    optionB: '',
    optionC: '',
    optionD: '',
    optionE: '',
    optionF: '',
    answer: '',
    explanation: ''
  });
}

// 重置按钮处理
function handleReset() {
  resetForm();
}

onMounted(() => {
  getQuestionData();
});
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
