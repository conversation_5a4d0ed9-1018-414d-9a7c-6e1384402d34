<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="题库管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <div class="flex items-center gap-12px">
          <span class="text-sm text-gray-500">总数量: {{ pagination.total }}</span>
          <NButton type="primary" @click="handleAddQuestion">
            新增题库
          </NButton>
        </div>
      </template>
      
      <div class="h-full flex-col">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="appStore.isMobile ? mobilePagination : pagination"
          class="sm:h-full"
        />
      </div>
    </NCard>

    <!-- 新增/编辑题库模态框 -->
    <NModal v-model:show="showModal" preset="card" :title="modalTitle" class="w-700px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="题目" path="question">
          <NInput v-model:value="formModel.question" placeholder="请输入题目" />
        </NFormItem>
        
        <NFormItem label="科目" path="subject">
          <NInput v-model:value="formModel.subject" placeholder="请输入科目" />
        </NFormItem>
        
        <NFormItem label="图片" path="image">
          <NUpload
            v-model:file-list="formModel.imageList"
            :max="1"
            list-type="image-card"
            @change="handleImageChange"
          >
            <NButton>上传图片</NButton>
          </NUpload>
        </NFormItem>

        <NFormItem label="预览图">
          <div class="w-full">
            <div class="grid grid-cols-2 gap-12px">
              <NFormItem label="A" path="optionA">
                <NInput v-model:value="formModel.optionA" placeholder="选项A" />
              </NFormItem>
              <NFormItem label="B" path="optionB">
                <NInput v-model:value="formModel.optionB" placeholder="选项B" />
              </NFormItem>
              <NFormItem label="C" path="optionC">
                <NInput v-model:value="formModel.optionC" placeholder="选项C" />
              </NFormItem>
              <NFormItem label="D" path="optionD">
                <NInput v-model:value="formModel.optionD" placeholder="选项D" />
              </NFormItem>
              <NFormItem label="E" path="optionE">
                <NInput v-model:value="formModel.optionE" placeholder="选项E" />
              </NFormItem>
              <NFormItem label="F" path="optionF">
                <NInput v-model:value="formModel.optionF" placeholder="选项F" />
              </NFormItem>
            </div>
          </div>
        </NFormItem>

        <NFormItem label="答案" path="answer">
          <NSelect
            v-model:value="formModel.answer"
            :options="answerOptions"
            placeholder="请选择正确答案"
          />
        </NFormItem>

        <NFormItem label="解析" path="explanation">
          <NInput
            v-model:value="formModel.explanation"
            type="textarea"
            :rows="3"
            placeholder="请输入解析"
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSubmit">提交</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, onMounted } from 'vue';
import type { Ref } from 'vue';
import { NButton, NTag, NPopconfirm } from 'naive-ui';
import type { DataTableColumns, FormInst, FormRules, UploadFileInfo } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useLoading } from '@sa/hooks';
import { $t } from '@/locales';
import { formatDateTime } from '@/utils/common';
import { fetchQuestionData, fetchCreateQuestion, fetchUpdateQuestion, fetchDeleteQuestion } from '@/service/api';
import { Api } from '@/typings/api';

defineOptions({
  name: 'QuestionBank'
});

const appStore = useAppStore();
const { loading, startLoading, endLoading } = useLoading();

// 表格数据
const data = ref<Api.QuestionBank.QuestionItem[]>([]);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  showQuickJumper: true,
  onUpdatePage: (page: number) => {
    pagination.page = page;
    getQuestionData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getQuestionData();
  }
});

// 模态框
const showModal = ref(false);
const modalTitle = ref('');
const formRef = ref<FormInst | null>(null);
const isEdit = ref(false);
const editingId = ref<number | null>(null);

// 表单数据
interface FormModel {
  question: string;
  subject: string;
  image: string;
  imageList: UploadFileInfo[];
  optionA: string;
  optionB: string;
  optionC: string;
  optionD: string;
  optionE: string;
  optionF: string;
  answer: string;
  explanation: string;
}

const formModel = reactive<FormModel>({
  question: '',
  subject: '',
  image: '',
  imageList: [],
  optionA: '',
  optionB: '',
  optionC: '',
  optionD: '',
  optionE: '',
  optionF: '',
  answer: '',
  explanation: ''
});

// 答案选项
const answerOptions = [
  { label: 'A', value: 'A' },
  { label: 'B', value: 'B' },
  { label: 'C', value: 'C' },
  { label: 'D', value: 'D' },
  { label: 'E', value: 'E' },
  { label: 'F', value: 'F' }
];

// 表单验证规则
const rules: FormRules = {
  question: [
    { required: true, message: '请输入题目', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入科目', trigger: 'blur' }
  ],
  optionA: [
    { required: true, message: '请输入选项A', trigger: 'blur' }
  ],
  optionB: [
    { required: true, message: '请输入选项B', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请选择正确答案', trigger: 'change' }
  ]
};

// 表格列定义
const columns = ref([
  {
    type: 'selection',
    disabled: () => false
  },
  {
    key: 'id',
    title: '题目ID',
    width: 100,
    align: 'center'
  },
  {
    key: 'qu_type',
    title: '题目类型',
    width: 100,
    align: 'center'
  },
  {
    key: 'qu_content',
    title: '题目',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: 'image',
    title: '题目图片',
    width: 120,
    render: row => {
      if (row.qu_img) {
        return <img src={row.qu_img} alt="题目图片" class="w-60px h-40px object-cover rounded" />;
      }
      return <span class="text-gray-400">无图片</span>;
    }
  },
  {
    key: 'status',
    title: '目前状态',
    width: 100,
    render: row => {
      const statusMap: Record<number, { label: string; type: 'success' | 'warning' | 'error' }> = {
        1: { label: '启用', type: 'success' },
        0: { label: '禁用', type: 'error' }
      };
      const status = statusMap[row.status] || { label: '未知', type: 'error' };
      return <NTag type={status.type}>{status.label}</NTag>;
    }
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: 180,
    render: row => formatDateTime(row.created_at)
  },
  {
    key: 'actions',
    title: '操作',
    width: 160,
    align: 'center',
    render: row => (
      <div class="flex gap-8px">
        <NButton size="small" type="primary" onClick={() => handleEdit(row)}>
          编辑
        </NButton>
        <NPopconfirm onPositiveClick={() => handleDelete(row)}>
          {{
            default: () => '确定删除这个题目吗？',
            trigger: () => (
              <NButton size="small" type="error">
                删除
              </NButton>
            )
          }}
        </NPopconfirm>
      </div>
    )
  }
]) as Ref<DataTableColumns<Api.QuestionBank.QuestionItem>>;

// 移动端分页
const mobilePagination = computed(() => {
  if (appStore.isMobile) {
    return {
      ...pagination,
      pageSize: 10,
      showSizePicker: false,
      showQuickJumper: false
    };
  }
  return false;
});

// 获取题库数据
async function getQuestionData() {
  startLoading();
  try {
    const { data: questionResponse, error } = await fetchQuestionData({
      page: pagination.page,
      page_size: pagination.pageSize
    });
    console.log("questionResponse==========="+JSON.stringify(questionResponse));
    if (!error && questionResponse) {
      data.value = questionResponse.questions || [];
      pagination.total = questionResponse.pagination.total || 0;
    } else {
      console.error('获取题库数据失败:', error);
      window.$message?.error('获取题库数据失败');
      data.value = [];
      pagination.total = 0;
    }

  } catch (error) {
    console.error('获取题库数据失败:', error);
    window.$message?.error('获取题库数据失败');
    data.value = [];
    pagination.total = 0;
  } finally {
    endLoading();
  }
}

// 新增题库
function handleAddQuestion() {
  modalTitle.value = '新增题库';
  isEdit.value = false;
  editingId.value = null;
  resetForm();
  showModal.value = true;
}

// 编辑题库
function handleEdit(row: Api.QuestionBank.QuestionItem) {
  modalTitle.value = '编辑题库';
  isEdit.value = true;
  editingId.value = row.id;

  // 填充表单数据
  Object.assign(formModel, {
    question: row.question,
    subject: row.subject,
    image: row.image,
    imageList: row.image ? [{ url: row.image, name: 'image', status: 'finished' }] : [],
    optionA: row.options.A,
    optionB: row.options.B,
    optionC: row.options.C,
    optionD: row.options.D,
    optionE: row.options.E,
    optionF: row.options.F,
    answer: row.answer,
    explanation: row.explanation
  });

  showModal.value = true;
}

// 删除题库
async function handleDelete(row: Api.QuestionBank.QuestionItem) {
  startLoading();
  try {
    const { error } = await fetchDeleteQuestion(row.id);

    if (!error) {
      window.$message?.success('删除成功');
      getQuestionData();
    } else {
      console.error('删除失败:', error);
      window.$message?.error('删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    window.$message?.error('删除失败');
  } finally {
    endLoading();
  }
}

// 图片上传处理
function handleImageChange({ fileList }: { fileList: UploadFileInfo[] }) {
  formModel.imageList = fileList;
  if (fileList.length > 0 && fileList[0].url) {
    formModel.image = fileList[0].url;
  } else {
    formModel.image = '';
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    startLoading();

    if (isEdit.value && editingId.value) {
      // 编辑模式
      const updateData: Api.QuestionBank.UpdateQuestionRequest = {
        id: editingId.value,
        question: formModel.question,
        subject: formModel.subject,
        image: formModel.image,
        options: {
          A: formModel.optionA,
          B: formModel.optionB,
          C: formModel.optionC,
          D: formModel.optionD,
          E: formModel.optionE,
          F: formModel.optionF
        },
        answer: formModel.answer,
        explanation: formModel.explanation
      };

      const { error } = await fetchUpdateQuestion(updateData);

      if (!error) {
        window.$message?.success('更新成功');
        showModal.value = false;
        getQuestionData();
      } else {
        console.error('更新失败:', error);
        window.$message?.error('更新失败');
      }
    } else {
      // 新增模式
      const submitData: Api.QuestionBank.CreateQuestionRequest = {
        question: formModel.question,
        subject: formModel.subject,
        image: formModel.image,
        options: {
          A: formModel.optionA,
          B: formModel.optionB,
          C: formModel.optionC,
          D: formModel.optionD,
          E: formModel.optionE,
          F: formModel.optionF
        },
        answer: formModel.answer,
        explanation: formModel.explanation
      };

      const { error } = await fetchCreateQuestion(submitData);

      if (!error) {
        window.$message?.success('创建成功');
        showModal.value = false;
        getQuestionData();
      } else {
        console.error('创建失败:', error);
        window.$message?.error('创建失败');
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    window.$message?.error('提交失败');
  } finally {
    endLoading();
  }
}

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    question: '',
    subject: '',
    image: '',
    imageList: [],
    optionA: '',
    optionB: '',
    optionC: '',
    optionD: '',
    optionE: '',
    optionF: '',
    answer: '',
    explanation: ''
  });
}

// 重置按钮处理
function handleReset() {
  resetForm();
}

onMounted(() => {
  getQuestionData();
});
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
