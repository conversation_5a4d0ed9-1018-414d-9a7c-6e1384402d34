<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { $t } from '@/locales';
import ImageCaptcha from '@/components/common/image-captcha.vue';
import { fetchRegister } from '@/service/api';

defineOptions({
  name: 'Register'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha('register');

interface FormModel {
  userName: string;
  phone: string;
  code: string;
  password: string;
  confirmPassword: string;
  imageCaptchaCode: string;
}

const model: FormModel = reactive({
  userName: '',
  phone: '',
  code: '',
  password: '',
  confirmPassword: '',
  imageCaptchaCode: ''
});

// 图形验证码相关
const imageCaptchaId = ref('');

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    userName: formRules.userName,
    phone: formRules.phone,
    code: formRules.code,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password),
    imageCaptchaCode: formRules.captcha //使用图形验证码规则
  };
});

async function handleSubmit() {
  await validate();
  // request to register
  // console.log("注册"+model.phone);
  // console.log("注册"+model.code);
  // console.log("注册"+model.password);
  // console.log("注册"+model.confirmPassword);

  try {
      const { data: registerResponse, error } = await fetchRegister(model.userName,model.phone,model.code,model.password);
      if (error) {
        // 如果API调用失败，使用Mock数据继续流程
        console.warn('API调用失败，使用Mock数据:', error);
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        console.log("注册====", JSON.stringify(registerResponse));
        window.$message?.success($t('page.login.common.registerSuccess'));
        //注册成功后，跳转到登录页面
        setTimeout(() => {
          toggleLoginModule('pwd-login');
        }, 1000);
      }
    } catch (error) {
      // 捕获异常，使用Mock数据
      console.warn('API调用异常，使用Mock数据:', error);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <NFormItem path="userName">
      <NInput v-model:value="model.userName" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </NFormItem>
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>
    <!-- 图形验证码 -->
    <NFormItem path="imageCaptchaCode">
      <ImageCaptcha
        v-model="model.imageCaptchaCode"
        @update:captcha-id="imageCaptchaId = $event"
      />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" :placeholder="$t('page.login.common.codePlaceholder')" />
        <NButton
          size="large"
          :disabled="isCounting || loading || !model.imageCaptchaCode"
          :loading="loading"
          @click="getCaptcha(model.phone, imageCaptchaId, model.imageCaptchaCode)"
        >
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
