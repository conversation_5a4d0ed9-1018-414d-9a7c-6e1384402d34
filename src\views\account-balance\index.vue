<script setup lang="ts">
import { onMounted, ref, h } from 'vue';
import { NCard, NSpace, NButton, NDataTable, NTag, NModal, NForm, NFormItem, NRadioGroup, NRadio, NInputNumber, useMessage } from 'naive-ui';
import { $t } from '@/locales';
import { fetchAccountBalance, fetchRechargeRecords, fetchCreateRecharge } from '@/service/api';
import type { Api } from '@/typings/api';

defineOptions({
  name: 'AccountBalance'
});

const message = useMessage();

// 账户余额
const balance = ref(0);
const loading = ref(false);

// 充值模态框
const showRechargeModal = ref(false);
const rechargeLoading = ref(false);
const rechargeForm = ref({
  amount: 0,
  paymentType: 'alipay' as 'alipay' | 'wechat'
});

// 充值记录
const rechargeRecords = ref<Api.Recharge.RechargeRecord[]>([]);
const recordsLoading = ref(false);
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0
});

// 表格列定义
const columns = [
  {
    title: '明细ID',
    key: 'id',
    width: 100
  },
  {
    title: '变动金额',
    key: 'amount',
    width: 120,
    render: (row: Api.Recharge.RechargeRecord) => {
      const balanceBefore = typeof row.balance_before === 'number' ? row.balance_before : 0;
      const balanceAfter = typeof row.balance_after === 'number' ? row.balance_after : 0;
      const changeAmount = balanceAfter - balanceBefore;

      // 根据变动金额确定颜色和符号
      const isIncrease = changeAmount > 0;
      const color = isIncrease ? '#d03050' : '#18a058'; // 绿色或红色
      const symbol = isIncrease ? '+' : '-'; // 正数显示+号，负数自带-号

      return h('span', {
        style: {
          color: color,
          fontWeight: '500'
        }
      }, `${symbol}${Math.abs(changeAmount).toFixed(2)}`);
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 120,
  },
  {
    title: '充值方式',
    key: 'payment_method',
    width: 100,
    render: (row: Api.Recharge.RechargeRecord) => {
      const typeMap = {
        alipay: { text: '支付宝', type: 'info' as const },
        wechat: { text: '微信', type: 'success' as const }
      };
      const config = typeMap[row.payment_method] || { text: '未知', type: 'default' as const };
      return h(NTag, { type: config.type }, () => config.text);
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: Api.Recharge.RechargeRecord) => {
      const statusMap = {
        pending: { text: '待支付', type: 'warning' as const },
        success: { text: '成功', type: 'success' as const },
        failed: { text: '失败', type: 'error' as const }
      };
      const config = statusMap[row.payment_status] || { text: '未知', type: 'default' as const };
      return h(NTag, { type: config.type }, () => config.text);
    }
  },
  {
    title: '时间',
    key: 'created_at',
    width: 180,
    render: (row: Api.Recharge.RechargeRecord) => {
      try {
        return row.created_at ? new Date(row.created_at).toLocaleString() : '-';
      } catch (error) {
        return '-';
      }
    }
  }
];

// 获取账户余额
const getAccountBalance = async () => {
  loading.value = true;
  try {
    const { data:balanceData, error } = await fetchAccountBalance();
    if (!error && balanceData) {
      // 确保 balance 是一个有效的数字
      balance.value = typeof balanceData.account_balance === 'number' ? balanceData.account_balance : 0;
    }
  } catch (error) {
    console.error('获取账户余额失败:', error);
    // 发生错误时设置默认值
    balance.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取充值记录
const getRechargeRecords = async (page = 1) => {
  recordsLoading.value = true;
  try {
    const { data:rechargeRecordData, error } = await fetchRechargeRecords({
      page,
      page_size: pagination.value.pageSize
    });
    console.log("rechargeRecordData==========="+JSON.stringify(rechargeRecordData));
    if (!error && rechargeRecordData) {
      // 确保 records 是一个数组，并且每个记录都有有效的数据
      rechargeRecords.value = Array.isArray(rechargeRecordData.transactions) ? rechargeRecordData.transactions : [];
      pagination.value.itemCount = typeof rechargeRecordData.total === 'number' ? rechargeRecordData.total : 0;
      pagination.value.page = page;
    }
  } catch (error) {
    console.error('获取充值记录失败:', error);
    // 发生错误时设置默认值
    rechargeRecords.value = [];
    pagination.value.itemCount = 0;
  } finally {
    recordsLoading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page: number) => {
  getRechargeRecords(page);
};

// 打开充值模态框
const openRechargeModal = () => {
  rechargeForm.value = {
    amount: 0,
    paymentType: 'alipay'
  };
  showRechargeModal.value = true;
};

// 提交充值
const handleRecharge = async () => {
  if (rechargeForm.value.amount <= 0) {
    message.error('请输入有效的充值金额');
    return;
  }
  rechargeLoading.value = true;
  try {
    const { data:rechargeData, error } = await fetchCreateRecharge({
      amount: rechargeForm.value.amount,
      payment_method: rechargeForm.value.paymentType
    });
    if (!error && rechargeData) {
      message.success('充值订单创建成功，请完成支付');
      showRechargeModal.value = false;
      
      // 这里可以根据返回的支付链接跳转到支付页面
      if (rechargeData.payment_url) {
        window.open(rechargeData.payment_url, '_blank');
      }
      
      // 刷新数据
      getAccountBalance();
      getRechargeRecords();
    }
  } catch (error) {
    console.error('创建充值订单失败:', error);
  } finally {
    rechargeLoading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  getAccountBalance();
  getRechargeRecords();
});
</script>

<template>
  <div class="h-full overflow-hidden">
    <NSpace vertical :size="16" class="h-full">
      <!-- 账户余额卡片 -->
      <NCard title="账户余额" :bordered="false" class="card-wrapper">
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="text-3xl font-bold text-primary">
              ¥{{ (typeof balance === 'number' ? balance : 0).toFixed(2) }}
            </div>
            <NButton
              type="primary"
              ghost
              size="small"
              @click="getAccountBalance"
              :loading="loading"
              class="refresh-btn"
            >
              <template #icon>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </template>
              刷新余额
            </NButton>
          </div>

          <div>
            <NButton type="primary" size="large" @click="openRechargeModal" class="recharge-btn">
              <template #icon>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </template>
              充值
            </NButton>
          </div>
        </div>
      </NCard>

      <!-- 余额明细 -->
      <NCard title="余额明细" :bordered="false" class="flex-1-hidden card-wrapper">
        <template #header-extra>
          <NButton @click="getRechargeRecords()" :loading="recordsLoading">
            刷新
          </NButton>
        </template>
        <NDataTable
          :columns="columns"
          :data="rechargeRecords"
          :loading="recordsLoading"
          :pagination="{
            page: pagination.page,
            pageSize: pagination.pageSize,
            itemCount: pagination.itemCount,
            showSizePicker: true,
            pageSizes: [10, 20, 50],
            onUpdatePage: handlePageChange,
            onUpdatePageSize: (pageSize: number) => {
              pagination.pageSize = pageSize;
              getRechargeRecords(1);
            }
          }"
          :scroll-x="600"
          class="h-full"
        />
      </NCard>
    </NSpace>

    <!-- 充值模态框 -->
    <NModal v-model:show="showRechargeModal" preset="dialog" title="账户充值">
      <NForm :model="rechargeForm" label-placement="left" label-width="100px">
        <NFormItem label="充值金额" required>
          <NInputNumber
            v-model:value="rechargeForm.amount"
            :min="1"
            :max="10000"
            :step="1"
            placeholder="请输入充值金额"
            class="w-full"
          >
            <template #prefix>¥</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="支付方式" required>
          <NRadioGroup v-model:value="rechargeForm.paymentType">
            <NSpace>
              <NRadio value="alipay">
                支付宝
              </NRadio>
              <NRadio value="wechat">
                微信支付
              </NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showRechargeModal = false">取消</NButton>
          <NButton type="primary" :loading="rechargeLoading" @click="handleRecharge">
            确认充值
          </NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.card-wrapper {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.refresh-btn {
  border: 1px solid var(--primary-color);
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recharge-btn {
  min-width: 140px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.recharge-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.recharge-btn:active {
  transform: translateY(0);
}
</style>
