#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\qingdao-xuefajianfen\soybean-admin-mainA\packages\scripts\node_modules;E:\qingdao-xuefajianfen\soybean-admin-mainA\packages\node_modules;E:\qingdao-xuefajianfen\soybean-admin-mainA\node_modules;E:\qingdao-xuefajianfen\node_modules;E:\node_modules;E:\qingdao-xuefajianfen\soybean-admin-mainA\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/scripts/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/packages/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules:/mnt/e/qingdao-xuefajianfen/node_modules:/mnt/e/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/tsx$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/tsx$exe"  "$basedir/../@sa/scripts/bin.ts" $args
  } else {
    & "$basedir/tsx$exe"  "$basedir/../@sa/scripts/bin.ts" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "tsx$exe"  "$basedir/../@sa/scripts/bin.ts" $args
  } else {
    & "tsx$exe"  "$basedir/../@sa/scripts/bin.ts" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
