import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * @description 登录
 * @param phone 手机号
 * @param password 密码
 * @returns
 */
export function fetchLogin(phone: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/auth/login',
    method: 'post',
    data: {
      phone,
      password
    }
  });
}

/**
 * @description 获取图形验证码
 * @returns
 */
export function fetchImageCaptcha() {
  return request<Api.ImageCaptcha.ImageCaptchaResponse>({
    url: '/api/v1/verification/captcha/generate',
    method: 'get'
  });
}

/**
 * @description 发送注册验证码
 * @param phone 手机号
 * @param captchaId 图形验证码ID
 * @param captchaCode 图形验证码
 * @returns
 */
export function fetchSendRegisterSms(phone: string, type: string, captchaId?: string, captchaCode?: string) {
  return request<Api.RegisterSms.RegisterSmsResponse>({
    url: '/api/v1/verification/sms/send',
    method: 'post',
    data: {
      phone,
      type,
      captcha_id: captchaId,
      captcha_code: captchaCode
    }
  });
}

/**
 * @description 注册
 * @param userName 用户名
 * @param code 短信验证码
 * @param password 密码
 * @param confirmPassword 确认密码
 * @returns
 */
export function fetchRegister(nickname: string, phone: string, sms_code: string, password: string) {
  return request<Api.Register.RegisterResponse>({
    url: '/api/v1/user/register',
    method: 'post',
    data: {
      team_name:nickname,
      phone,
      sms_code,
      password
    }
  });
}

/**
 * @description 忘记密码 - 发送验证码
 * @param phone 手机号
 * @param type 类型
 * @param captchaId 图形验证码ID
 * @param captchaCode 图形验证码
 * @returns
 */
export function fetchSendResetPasswordSms(phone: string, type: string, captchaId?: string, captchaCode?: string) {
  return request<Api.ResetPasswordSms.ResetPasswordSmsResponse>({
    url: '/api/v1/verification/sms/send',
    method: 'post',
    data: {
      phone,
      type,
      captcha_id: captchaId,
      captcha_code: captchaCode
    }
  });
}

/**
 * @description 重置密码
 * @param phone 手机号
 * @param sms_code 短信验证码
 * @param new_password 新密码
 * @returns
 */
export function fetchResetPassword(phone: string, sms_code: string, new_password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/user/reset-password',
    method: 'post',
    data: {
      phone,
      sms_code,
      new_password
    }
  });
}

/**
 * @description 获取用户信息
 */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({
    url: '/api/v1/user/profile',
    method: 'get'
  });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * @description 获取账户余额
 * @returns
 */
export function fetchAccountBalance() {
  return request<Api.Account.BalanceResponse>({
    url: '/api/v1/billing/balance',
    method: 'get'
  });
}

/**
 * @description 获取充值记录
 * @param params 分页参数
 * @returns
 */
export function fetchRechargeRecords(params: Api.Recharge.RechargeRecordsParams) {
  return request<Api.Recharge.RechargeRecordsResponse>({
    url: '/api/v1/billing/transactions',
    method: 'get',
    params
  });
}

/**
 * @description 创建充值订单
 * @param data 充值数据
 * @returns
 */
export function fetchCreateRecharge(data: Api.Recharge.CreateRechargeRequest) {
  return request<Api.Recharge.CreateRechargeResponse>({
    url: '/api/v1/billing/recharge',
    method: 'post',
    data
  });
}

/**
 * @description 获取用户数据列表（分页）
 * @param params 分页参数
 * @returns
 */
export function fetchUserData(params: Api.Auth.UserDataParams) {
  return request<Api.Auth.UserListResponse>({
    url: '/api/v1/admin/users',
    method: 'get',
    params
  });
}

/**
 * @description 更新用户状态（封禁/解封）
 * @param userId 用户ID
 * @param status 状态（1=正常，2=封禁）
 * @returns
 */
export function fetchUpdateUserStatus(userId: number, status: number) {
  return request({
    url: `/api/v1/admin/users/${userId}`,
    method: 'put',
    data: {
      status
    }
  });
}

/**
 * @description 获取题库数据列表（分页）
 * @param params 分页参数
 * @returns
 */
export function fetchQuestionData(params: Api.QuestionBank.QuestionDataParams) {
  return request<Api.QuestionBank.QuestionListResponse>({
    url: '/api/v1/admin/questions',
    method: 'get',
    params
  });
}

/**
 * @description 创建题库
 * @param data 题库数据
 * @returns
 */
export function fetchCreateQuestion(data: Api.QuestionBank.CreateQuestionRequest) {
  return request<Api.QuestionBank.CreateQuestionResponse>({
    url: '/api/v1/admin/questions',
    method: 'post',
    data
  });
}

/**
 * @description 删除题库
 * @param questionId 题库ID
 * @returns
 */
export function fetchDeleteQuestion(questionId: number) {
  return request({
    url: `/api/v1/admin/questions/${questionId}`,
    method: 'delete'
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
