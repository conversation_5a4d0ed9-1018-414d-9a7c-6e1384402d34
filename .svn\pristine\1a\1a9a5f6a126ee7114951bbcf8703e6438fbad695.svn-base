!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).AlovaMock={})}(this,(function(e){"use strict";const t="undefined",r=Promise,s=Object,o=void 0,n=!0,a=!1,c=e=>typeof e;typeof window===t&&typeof process!==t&&process.cwd;const u=e=>"function"===c(e),i=e=>"string"===c(e),p=e=>s.prototype.toString.call(e);const l=(e,...t)=>new e(...t),d="color: black; font-size: 12px; font-weight: bolder",h=e=>{const t={};for(const r in e)t[r]={value:e[r]};return t},f=({isMock:e,url:t,method:r,headers:s,query:o,data:n,responseHeaders:a,response:c})=>{const u=console;u.groupCollapsed("%c"+(e?"mock":"Realtime"),`padding: 2px 6px; background: ${e?"#ccefff":"#ededed"}; color: ${e?"#64bde8":"#999999"};`,t),u.log("%c[Method]",d,r.toUpperCase()),u.log("%c[Request Headers]",d),u.table(h(s)),u.log("%c[Query String Parameters]",d),u.table(h(o)),u.log("%c[Request Body]",d,n||""),e&&(Object.keys(a).length>0&&(u.log("%c[Response Headers]",d),u.table(h(a))),u.log("%c[Response Body]",d,c||"")),u.groupEnd()},m=({status:e=200,responseHeaders:t,statusText:r="ok",body:s})=>{const o=new Response((e=>{const t=p(e);return/^\[object (Blob|FormData|ReadableStream|URLSearchParams)\]$/i.test(t)||(r=ArrayBuffer,e instanceof r);var r})(s)?s:JSON.stringify(s),{status:e,statusText:r,headers:t});return{response:o,headers:o.headers}},y=e=>e;function b({enable:e=true,delay:t=2e3,httpAdapter:s,mockRequestLogger:d=f,mock:h,onMockResponse:b=m,onMockError:g=y,matchMode:T="pathname"}={mock:{}}){return(f,m)=>{h=e&&h||{};const{url:y,data:k,type:E,headers:q}=f;let R=m.url,v=m.config.params||{};if("pathname"===T){const e=(e=>{const t=(e=/^[^/]*\/\//.test(e)?e:`//${e}`).split("/").slice(3),r={};let s=t.pop(),o="",n="";return s&&(s=s.replace(/\?[^?#]+/,(e=>(e.substring(1).split("&").forEach((e=>{const[t,s]=e.split("=");t&&(r[t]=s)})),""))),s=s.replace(/#[^#]*/,(e=>(n=e,""))),t.push(s),o=`/${t.join("/")}`),{pathname:o,query:r,hash:n}})(y);R=e.pathname,v=e.query}const w={},x=R.split("/"),j=Object.keys(h).filter((e=>{if(e.startsWith("-"))return a;let t="GET";if(e=e.replace(/^\[(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\]/i,((e,r)=>(t=r.toUpperCase(),""))),t!==E.toUpperCase())return a;const r=e.split("/");if(r.length!==x.length)return a;for(let e=0;e<r.length;e+=1){const t=r[e],s=(t.match(/^\{(.*)\}$/)||["",""])[1];if(s)w[s]=x[e];else if(t!==x[e])return a}return n}));let H=j.find((e=>!/\{.*\}/.test(e)));H=H||j.shift();const M=H?h[H]:o;if(M===o){if(s)return u(d)&&d({isMock:a,url:y,method:E,params:w,headers:q,query:v,data:{},responseHeaders:{}}),s(f,m);throw new Error(`cannot find the httpAdapter.\n[url]${y}`)}const P=function(){let e,t;return{promise:new Promise(((r,s)=>{e=r,t=s})),resolve:e,reject:t}}(),{resolve:A}=P;let{promise:O,reject:C}=P;const N=m.config.timeout||0;N>0&&setTimeout((()=>{C(new Error("request timeout"))}),N);const S=setTimeout((()=>{try{const e=u(M)?M({query:v,params:w,data:i(k)?(()=>{try{return JSON.parse(k)}catch(e){return k}})():k||{},headers:q}):M;A(l(Promise,((t,s)=>{var o;C=s,(o=e,r.resolve(o)).then(t).catch(s)})))}catch(e){C(e)}}),t);return O=O.then((e=>{let t=200,r="ok",s={},a=o;var p;return e===o?(t=404,r="api not found"):e&&(p=e.status,"number"===c(p)&&!Number.isNaN(p))&&i(e.statusText)?(t=e.status,r=e.statusText,s=e.responseHeaders||s,a=e.body):a=e,l(Promise,((e,o)=>{try{e(b({status:t,statusText:r,responseHeaders:s,body:a},{headers:q,query:v,params:w,data:k||{}},m))}catch(e){o(e)}})).then((e=>(u(d)&&d({isMock:n,url:y,method:E,params:w,headers:q,query:v,data:k||{},responseHeaders:s,response:a}),e)))})).catch((e=>{return t=g(e,m),r.reject(t);var t})),{response:()=>O.then((({response:e})=>e&&"[object Response]"===p(e)?e.clone():e)),headers:()=>O.then((({headers:e})=>e)),abort:()=>{clearTimeout(S),C(new Error("The user abort request"))}}}}e.createAlovaMockAdapter=function(e,t={enable:!0}){let r={};return e.filter((({enable:e})=>e)).forEach((({data:e})=>{r={...e,...r}})),b({...t,mock:r})},e.defineMock=(e,t=!0)=>({enable:t,data:e})}));
