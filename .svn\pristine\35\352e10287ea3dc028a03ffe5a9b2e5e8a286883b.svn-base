#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/which@2.0.2/node_modules:/mnt/e/qingdao-xuefajianfen/soybean-admin-mainA/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../which/bin/node-which" "$@"
else
  exec node  "$basedir/../which/bin/node-which" "$@"
fi
