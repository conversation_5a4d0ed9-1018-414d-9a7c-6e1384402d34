<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="应用管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <div class="flex items-center gap-12px">
          <!-- 调试信息 -->
          <NTag type="info" size="small">
            当前用户角色: {{ authStore.userInfo.role.join(', ') || '无' }}
          </NTag>
          <NButton type="primary" size="small" @click="handleAddApp">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加应用
          </NButton>
        </div>
      </template>
      <div class="h-full flex-col">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="appStore.isMobile ? mobilePagination : pagination"
          class="sm:h-full"
        />
      </div>
    </NCard>

    <!-- 添加/编辑应用弹窗 -->
    <NModal v-model:show="showModal" preset="card" :title="modalTitle" class="w-600px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" :label-width="100">
        <div class="flex-col gap-16px">
          <NFormItem label="应用昵称" path="nickname">
            <NInput v-model:value="formModel.nickname" placeholder="请输入应用昵称" />
          </NFormItem>
          <NFormItem label="应用图标" path="icon">
            <NInput v-model:value="formModel.icon" placeholder="请输入应用图标URL（必填）" />
          </NFormItem>
          <NFormItem label="应用白名单" path="whitelist">
            <NInput
              v-model:value="formModel.whitelist"
              type="textarea"
              placeholder="请输入IP白名单，多个IP用中文逗号分隔（必填）&#10;例如：* （允许所有IP）或 ***********，127.0.0.1，********"
              :rows="3"
              @blur="handleWhitelistBlur"
            />
          </NFormItem>
        </div>
      </NForm>
      <template #footer>
        <div class="flex-y-center justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, onMounted } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableColumns, FormInst, FormRules } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useLoading } from '@sa/hooks';
import { Api } from '@/typings/api';
import { fetchAppList, fetchCreateApp, fetchUpdateApp, fetchDeleteApp } from '@/service/api';

defineOptions({
  name: 'AppManagement'
});

const appStore = useAppStore();
const authStore = useAuthStore();

// interface UserData {
//   id: number;
//   username: string;
//   email: string;
//   phone: string;
//   role: string;
//   status: 'active' | 'inactive';
//   is_active: number;
//   createTime: string;
// }

interface FormModel {
  nickname: string;
  icon: string;
  whitelist: string;
}

const { loading, startLoading, endLoading } = useLoading(false);

// 表格数据
const data = ref<Api.App.AppListItem[]>([]);

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  showQuickJumper: true,
  onUpdatePage: (page: number) => {
    pagination.page = page;
    getAppData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getAppData();
  }
});

// 表格列配置
const columns: Ref<DataTableColumns<Api.App.AppListItem>> = ref([
  {
    type: 'selection'
  },
  {
    key: 'index',
    title: '序号',
    width: 80,
    align: 'center',
    render: (_, index) => {
      return (pagination.page - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    title: 'App ID',
    key: 'id',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: 'App Key',
    key: 'app_key',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      if (!row.app_key) return '-';
      const maskedKey = row.app_key.substring(0, 8) + '****' + row.app_key.substring(row.app_key.length - 4);
      return <NTag type="info" size="small">{maskedKey}</NTag>;
    }
  },
  {
    title: '应用昵称',
    key: 'app_name',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '应用图标',
    key: 'logo_url',
    width: 100,
    render: (row) => {
      if (!row.logo_url) return '-';
      return (
        <div class="flex items-center justify-center">
          <img src={row.logo_url} alt="应用图标" class="w-8 h-8 rounded" />
        </div>
      );
    }
  },
  {
    title: 'IP白名单',
    key: 'ip_whitelist',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      if (!row.ip_whitelist) return '-';
      // 支持中文逗号和英文逗号分割
      const ips = row.ip_whitelist.split(/[,，]/).map(ip => ip.trim()).filter(ip => ip !== '');
      if (ips.length <= 2) {
        return ips.join('，');
      }
      return `${ips.slice(0, 2).join('，')} 等${ips.length}个IP`;
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return (
        <div class="flex gap-8px">
          <NButton
            type="primary"
            size="small"
            onClick={() => handleEditApp(row)}
          >
            修改
          </NButton>
          <NButton
            type="error"
            size="small"
            onClick={() => handleDeleteApp(row.id)}
          >
            删除
          </NButton>
        </div>
      );
    }
  }
]) as Ref<DataTableColumns<Api.App.AppListItem>>;

// 移动端分页
const mobilePagination = computed(() => {
  if (appStore.isMobile) {
    return {
      ...pagination,
      pageSize: 10,
      showSizePicker: false,
      showQuickJumper: false
    };
  }
  return false;
});

// 弹窗相关
const showModal = ref(false);
const modalTitle = ref('');
const formRef = ref<FormInst | null>(null);
const isEdit = ref(false);
const editingId = ref<number | null>(null);

// 表单数据
const formModel = reactive<FormModel>({
  nickname: '',
  icon: '',
  whitelist: ''
});

// 图片URL验证函数
function validateImageURL(url: string): boolean {
  if (!url || url.trim() === '') {
    return false;
  }

  const trimmedUrl = url.trim();

  // 验证URL格式
  try {
    new URL(trimmedUrl);
  } catch {
    return false;
  }

  // 验证图片文件扩展名
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i;
  return imageExtensions.test(trimmedUrl);
}

// 图片URL验证规则函数
function validateImageURLRule(_rule: any, value: string): boolean | Error {
  if (!value || value.trim() === '') {
    return new Error('请输入应用图标URL');
  }

  if (!validateImageURL(value)) {
    return new Error('请输入有效的图片URL（支持格式：jpg、jpeg、png、gif、bmp、webp、svg）');
  }

  return true;
}

// IP地址验证函数
function validateIP(ip: string): boolean {
  const trimmedIp = ip.trim();

  // 支持 * 作为默认值（允许所有IP）
  if (trimmedIp === '*') {
    return true;
  }

  // 验证标准IPv4地址格式
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(trimmedIp);
}

// 格式化IP白名单输入（将英文逗号转换为中文逗号）
function formatIPWhitelist(value: string): string {
  if (!value) return '';
  return value.replace(/,/g, '，').replace(/\s+/g, '');
}

// IP白名单验证函数（支持 * 表示允许所有IP）
function validateIPWhitelist(_rule: any, value: string): boolean | Error {
  if (!value || value.trim() === '') {
    return new Error('请输入IP白名单'); // 必填字段，为空时返回错误
  }

  // 支持中文逗号和英文逗号分割
  const ips = value.split(/[,，]/).map(ip => ip.trim()).filter(ip => ip !== '');

  if (ips.length === 0) {
    return new Error('请输入有效的IP地址'); // 如果分割后没有有效IP，返回错误
  }

  // 验证每个IP地址格式（支持 * 和标准IPv4格式）
  for (const ip of ips) {
    if (!validateIP(ip)) {
      return new Error(`IP地址格式不正确: ${ip}（支持格式：* 或 ***********）`);
    }
  }

  return true; // 所有IP都验证通过
}

// 表单验证规则
const rules: FormRules = {
  nickname: [
    { required: true, message: '请输入应用昵称', trigger: 'blur' },
    { min: 2, max: 50, message: '应用昵称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  icon: [
    { required: true, validator: validateImageURLRule, trigger: 'blur' }
  ],
  whitelist: [
    { required: true, validator: validateIPWhitelist, trigger: 'blur' }
  ]
};

// 获取应用数据
async function getAppData() {
  startLoading();
  try {
    console.log('🚀 开始获取应用数据，页码:', pagination.page, '每页大小:', pagination.pageSize);

    const result = await fetchAppList(pagination.page, pagination.pageSize);
    console.log('📦 API完整响应:', result);

    const { data: appResponse, error } = result;
    console.log('📊 解构后的数据:', JSON.stringify(appResponse));

    if (!error && appResponse) {
      console.log('✅ 数据获取成功');
      console.log('📋 应用列表:', appResponse.applications);
      console.log('📈 总数:', appResponse.total);

      data.value = appResponse.applications || [];
      pagination.total = appResponse.total || 0;

      console.log('🎯 设置后的data.value:', data.value);
      console.log('🎯 设置后的pagination.total:', pagination.total);

      if (data.value.length === 0) {
        console.warn('⚠️ 数据列表为空');
      }
    } else {
      console.error('❌ API返回错误或数据为空:', error);
      window.$message?.error('获取应用数据失败: ' + (error || '未知错误'));
      data.value = [];
    }
  } catch (error) {
    console.error('💥 获取应用数据异常:', error);
    window.$message?.error('获取应用数据失败: ' + error);
    data.value = [];
  } finally {
    endLoading();
  }
}

// 添加应用
function handleAddApp() {
  isEdit.value = false;
  modalTitle.value = '添加应用';
  resetForm();
  showModal.value = true;
}

// 编辑应用
function handleEditApp(row: Api.App.AppListItem) {
  isEdit.value = true;
  modalTitle.value = '编辑应用';
  editingId.value = row.id;
  formModel.nickname = row.app_name;
  formModel.icon = row.logo_url || '';
  formModel.whitelist = row.ip_whitelist || '';
  showModal.value = true;
}

// 删除应用
async function handleDeleteApp(id: number) {
  window.$dialog?.warning({
    title: '确认删除',
    content: '确定要删除这个应用吗？删除后无法恢复。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      startLoading();
      try {
        const { error } = await fetchDeleteApp(id);
        if (!error) {
          window.$message?.success('删除成功');
          await getAppData();
        } else {
          window.$message?.error('删除失败');
        }
      } catch (error) {
        window.$message?.error('删除失败');
      } finally {
        endLoading();
      }
    }
  });
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    startLoading();

    if (isEdit.value && editingId.value) {
      // 编辑应用
      const { error } = await fetchUpdateApp({
        id: editingId.value,
        nickname: formModel.nickname,
        icon: formModel.icon || undefined,
        whitelist: formModel.whitelist || undefined
      });

      if (!error) {
        window.$message?.success('编辑成功');
        await getAppData();
        closeModal();
      } else {
        window.$message?.error('编辑失败');
      }
    } else {
      // 添加应用
      const {data: createAppData, error } = await fetchCreateApp({
        app_name: formModel.nickname,
        logo_url: formModel.icon, // 必填字段，已通过验证
        ip_whitelist: formModel.whitelist // 必填字段，已通过验证
      });
      console.log("createAppData========"+JSON.stringify(createAppData));
      if (!error && createAppData) {
        window.$message?.success('添加成功');
        await getAppData();
        closeModal();
      } else {
        window.$message?.error('添加失败');
      }
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    endLoading();
  }
}

// 处理白名单输入格式化
function handleWhitelistBlur() {
  if (formModel.whitelist) {
    formModel.whitelist = formatIPWhitelist(formModel.whitelist);
  }
}

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    nickname: '',
    icon: '',
    whitelist: ''
  });
}

// 关闭弹窗
function closeModal() {
  showModal.value = false;
  resetForm();
  editingId.value = null;
}

// 初始化
onMounted(() => {
  getAppData();
});
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
