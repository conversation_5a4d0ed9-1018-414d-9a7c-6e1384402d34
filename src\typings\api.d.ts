import Register from '@/views/_builtin/login/modules/register.vue';
import { namespace } from 'naive-ui/es/_utils/cssr';

/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refresh_token?: string;
      expires_in?: number;
      user?: {
        id: number;
        phone: string;
        team_name: string;
        role: string;
      };
    }

    interface UserInfo {
      //余额
      balance: number;
      //创建时间
      created_at: string;
      //用户id
      id: number;
      //是否激活
      is_active: number;
      //昵称
      nickname: string;
      //团队名称/用户名
      team_name: string;
      //手机号
      phone: string;
      //角色身份
      role: string[];
      //更新时间
      updated_at: string;
    }

    /** 用户列表项 */
    interface UserListItem {
      //用户id
      id: number;
      //手机号码
      phone: string;
      //团队名称/用户名
      team_name: string;
      //角色身份
      role: string;
      //账户余额
      account_balance: number;
      //资源配额
      resource_quota: number;
      //状态
      status: number;
      //创建时间
      created_at: string;
      //更新时间
      updated_at: string;

      //后端返回数据结构示例
      // {
      //   "id": 4,
      //   "phone": "***********",
      //   "team_name": "陈憨憨1",
      //   "role": "tenant",
      //   "account_balance": 3850,
      //   "resource_quota": 3000,
      //   "status": 1,
      //   "created_at": "2025-07-22T22:44:40.697+08:00",
      //   "updated_at": "2025-07-24T11:15:33.843+08:00"
      // }
    }

    /** 用户列表响应 */
    interface UserListResponse {
      /** 用户列表数据 */
      users: UserListItem[];
      /** 当前页码 */
      page: number;
      /** 每页大小 */
      page_size: number;
      /** 总数量 */
      total: number;
    }

    /** 用户数据查询参数 */
    interface UserDataParams {
      page: number;
      page_size: number;
    }
  }

  /**
   * @description 图形验证码参数
   */
  namespace ImageCaptcha {
    interface ImageCaptchaResponse {
      id: string;
      base64: string; // 完整的base64 data URL格式图片
    }
  }

  /**
   * @description 注册验证码参数
   */
  namespace RegisterSms {
    interface RegisterSmsResponse {
      code: string;
    }
    interface RegisterSmsParams {
      phone: string;
      captcha_id?: string;
      captcha_code?: string;
    }
  }

  /**
   * @description 重置密码验证码参数
   */
  namespace ResetPasswordSms {
    interface ResetPasswordSmsResponse {
      code: string;
      token: string;
    }
    interface ResetPasswordSmsParams {
      phone: string;
      captcha_id?: string;
      captcha_code?: string;
    }
  }

  /**
   * @description 注册参数
   */
  namespace Register {
    interface RegisterResponse {
      id: number;
      phone: string;
      nickname: string;
      is_active: number;
      status: string;
    }
  }

  /**
   * @description 账户余额相关
   */
  namespace Account {
    interface BalanceResponse {
      account_balance: number;
    }
  }

  /**
   * @description 题库管理相关
   */
  namespace QuestionBank {
    /** 题目选项 */
    interface QuestionOptions {
      A: string;
      B: string;
      C: string;
      D: string;
      E: string;
      F: string;
    }

    /** 题库列表项 */
    interface QuestionItem {
      /** 题库ID */
      id: number;
      /** 题目 */
      question: string;
      /** 科目 */
      subject: string;
      /** 题目图片 */
      image: string;
      /** 选项 */
      options: QuestionOptions;
      /** 正确答案 */
      answer: string;
      /** 解析 */
      explanation: string;
      /** 状态 */
      status: number;
      /** 创建时间 */
      created_at: string;
      /** 更新时间 */
      updated_at: string;
    }

    /** 题库列表响应 */
    interface QuestionListResponse {
      /** 题库列表数据 */
      questions: QuestionItem[];
      /** 当前页码 */
      page: number;
      /** 每页大小 */
      page_size: number;
      /** 总数量 */
      total: number;
    }

    /** 题库查询参数 */
    interface QuestionDataParams {
      page: number;
      page_size: number;
    }

    /** 创建题库请求 */
    interface CreateQuestionRequest {
      /** 题目 */
      question: string;
      /** 科目 */
      subject: string;
      /** 题目图片 */
      image?: string;
      /** 选项 */
      options: QuestionOptions;
      /** 正确答案 */
      answer: string;
      /** 解析 */
      explanation: string;
    }

    /** 创建题库响应 */
    interface CreateQuestionResponse {
      /** 题库ID */
      id: number;
      /** 题目 */
      question: string;
      /** 状态 */
      status: string;
    }
  }

  /**
   * @description 充值相关
   */
  namespace Recharge {
    interface RechargeRecord {
      id: number;
      transaction_type: string;
      order_no: string;
      amount: number;
      payment_method: 'alipay' | 'wechat';
      payment_status: 'pending' | 'success' | 'failed';
      description: string;
      created_at: string;
      updated_at: string;
      balance_before: number;
      balance_after:number;
    }

    interface RechargeRecordsResponse {
      transactions: RechargeRecord[];
      total: number;
      page: number;
      pageSize: number;
    }

    interface RechargeRecordsParams {
      page: number;
      page_size: number;
    }

    interface CreateRechargeRequest {
      amount: number;
      payment_method: 'alipay' | 'wechat';
    }

    interface CreateRechargeResponse {
      transaction_id: number;
      order_no: string;
      amount: number;
      payment_type: 'alipay' | 'wechat';
      status: 'pending';
      payment_url?: string;
      qr_code?: string;
    }
  }

  /** 应用管理相关接口 */
    namespace App {
      /** 应用列表项 */
      interface AppListItem {
        /** 应用ID */
        id: number;
        /** 应用标识 */
        appid: string;
        /** 应用密钥 */
        app_key: string;
        /** 应用昵称 */
        app_name: string;
        /** 应用图标 */
        logo_url?: string;
        /** 应用白名单 */
        ip_whitelist?: string;
        /** 创建时间 */
        created_at: string;
        /** 更新时间 */
        updated_at: string;
      }

      /** 应用列表响应 */
      interface AppListResponse {
        /** 应用列表数据 */
        applications: AppListItem[];
        /** 当前页码 */
        page: number;
        /** 每页大小 */
        page_size: number;
        /** 总数量 */
        total: number;
      }

      /** 创建应用请求 */
      interface CreateAppRequest {
        /** 应用昵称 */
        app_name: string;
        /** 应用图标 */
        logo_url: string;
        /** 应用白名单 */
        ip_whitelist: string;
      }

      /** 更新应用请求 */
      interface UpdateAppRequest {
        /** 应用ID */
        id: number;
        /** 应用昵称 */
        nickname: string;
        /** 应用图标 */
        icon?: string;
        /** 应用白名单 */
        whitelist?: string;
      }
  }

  /**
   * @description 资源包相关
   */
  namespace Package {
    /** 资源包信息 */
    interface PackageItem {
      /** 资源包ID */
      id: number;
      /** 资源包名称 */
      package_name: string;
      /** 资源包描述 */
      package_description: string;
      /** 配额数量 */
      quota_amount: number;
      /** 价格 */
      price: number;
      /** 有效天数 */
      validity_days: number;
      /** 排序顺序 */
      sort_order: number;
    }

    /** 资源包列表响应 */
    interface PackageListResponse {
      code: number;
      message: string;
      data: PackageItem[];
      request_id: string;
      timestamp: number;
    }

    /** 购买资源包请求 */
    interface PurchasePackageRequest {
      /** 资源包ID */
      package_id: number;
      /** 购买支付方式 */
      payment_method:string;
    }

    /** 购买资源包响应 */
    interface PurchasePackageResponse {
      /** 订单ID */
      order_id: string;
      /** 订单编号 */
      order_no: string;
      /** 资源包信息 */
      package_info: PackageItem;
      /** 购买时间 */
      purchase_time: string;
      /** 剩余余额 */
      account_balance: number;
    }

    /** 资源包购买记录 */
    interface PackagePurchaseRecord {
      /** 记录ID */
      id: number;
      /** 订单ID */
      order_id: number;
      /** 资源包名称 */
      package_name: string;
      /** 总配额数量 */
      total_quota: number;
      /** 已使用配额 */
      used_quota: number;
      /** 剩余配额 */
      remaining_quota: number;
      /** 状态 */
      status: number;
      /** 到期时间 */
      expires_at: string;
      /** 用完时间 */
      used_up_at: string | null;
      /** 创建时间 */
      created_at: string;
    }

    /** 资源包购买记录响应 */
    interface PackagePurchaseRecordsResponse {
      packages: PackagePurchaseRecord[];
      total: number;
      page: number;
      page_size: number;
    }

    /** 资源包购买记录查询参数 */
    interface PackagePurchaseRecordsParams {
      page: number;
      page_size: number;
      package_id?: number;
    }
    }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }
}
